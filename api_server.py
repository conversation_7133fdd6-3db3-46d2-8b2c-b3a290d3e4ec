"""API Server for the Agent Evaluation Tool."""

import os
import logging
import argparse
from pathlib import Path

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.engine.config import ServerConfig
from src.engine.queue import get_task_queue
from src.storage.db import get_storage
from src.api.routes import router as api_router

# Note: Logging will be configured by the Config class
logger = logging.getLogger(__name__)




async def startup_event():
    """Startup event handler."""
    logger.info("Starting Agent Evaluation Tool API Server")

    # Load configuration
    config_path = os.environ.get("CONFIG_PATH")
    config = ServerConfig(config_path)

    # Initialize task queue
    max_queue_size = config.queue_max_size
    get_task_queue(max_size=max_queue_size)

    # Initialize storage with server config
    get_storage(config)

    # Note: Worker pool initialization is removed from API server
    # Workers are started separately using evaluation_worker.py

    logger.info("Agent Evaluation Tool API Server started")


async def shutdown_event():
    """Shutdown event handler."""
    logger.info("Shutting down Agent Evaluation Tool API Server")

    # Note: Worker pool shutdown is removed from API server
    # Workers are managed separately

    logger.info("Agent Evaluation Tool API Server shut down")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    # Load configuration first to set up logging
    config_path = os.environ.get("CONFIG_PATH")
    config = ServerConfig(config_path)

    # Get API configuration
    debug_mode = config.api_debug

    # Create FastAPI app
    app = FastAPI(
        title="Agent Evaluation Tool API Server",
        description="API server for evaluating LLM agents on various benchmarks",
        version="0.1.0",
        debug=debug_mode
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API routes
    app.include_router(api_router, prefix="/api")

    # Add event handlers
    app.add_event_handler("startup", startup_event)
    app.add_event_handler("shutdown", shutdown_event)

    return app

# Create the app
app = create_app()


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Agent Evaluation Tool API Server")
    parser.add_argument("--config", help="Path to custom configuration file (defaults to config/server.yaml)")
    parser.add_argument("--host", help="Host to bind to (overrides config)")
    parser.add_argument("--port", type=int, help="Port to bind to (overrides config)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Set default config path for server if not specified
    config_path = args.config
    if not config_path:
        # Use server.yaml as default
        server_config = Path("config/server.yaml")
        if server_config.exists():
            config_path = str(server_config)
        else:
            raise FileNotFoundError("Server configuration file not found. Please ensure config/server.yaml exists.")

    # Set environment variables
    if config_path:
        os.environ["CONFIG_PATH"] = config_path

    # Load configuration to get default values
    config = ServerConfig(config_path)

    # Use command line args if provided, otherwise use config values
    host = args.host if args.host else config.api_host
    port = args.port if args.port else config.api_port

    # Run the application
    uvicorn.run(
        "api_server:app",
        host=host,
        port=port,
        reload=args.reload
    )
