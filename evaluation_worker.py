"""Evaluation Worker entry point for the Agent Evaluation Tool."""

import os
import asyncio
import logging
import argparse
from typing import Optional
from pathlib import Path

from src.engine.config import WorkerConfig
from src.engine.worker import Worker
from src.adapters.registry import initialize_default_adapters, create_adapters_from_config

# Note: Logging will be configured by the Config class
logger = logging.getLogger(__name__)


async def initialize_inference_backends(config: WorkerConfig):
    """Initialize inference backends from configuration.

    Args:
        config: WorkerConfig object.
    """
    try:
        from src.inference_backends.manager import backend_manager

        llm_endpoints = config.llm_endpoints
        if not llm_endpoints:
            logger.warning("No LLM endpoints found in configuration")
            return

        # Load backends from configuration
        backend_manager.load_backends_from_config(llm_endpoints)

        logger.info(f"Initialized {len(backend_manager.list_backends())} inference backends: {backend_manager.list_backends()}")

    except Exception as e:
        logger.error(f"Failed to initialize inference backends: {e}")
        raise


async def run_worker(worker_id: str, config_path: Optional[str] = None):
    """Run an evaluation worker.

    Args:
        worker_id: Worker ID.
        config_path: Path to custom configuration file.
    """
    # Load configuration first to set up logging
    config = WorkerConfig(config_path)

    logger.info(f"Starting evaluation worker {worker_id}")

    # Initialize inference backends from configuration
    await initialize_inference_backends(config)

    # Initialize default adapters
    initialize_default_adapters()

    # Create adapter instances from configuration
    adapters = create_adapters_from_config(config)

    # Create and start worker
    max_concurrent_tasks = config.max_concurrent_tasks
    timeout_seconds = config.timeout_seconds
    model_concurrency = config.model_concurrency

    worker = Worker(
        worker_id=worker_id,
        adapters=adapters,
        max_concurrent_tasks=max_concurrent_tasks,
        timeout_seconds=timeout_seconds,
        model_concurrency=model_concurrency,
        config=config
    )

    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info(f"Evaluation worker {worker_id} interrupted")
        await worker.stop()
    except Exception as e:
        logger.error(f"Evaluation worker {worker_id} encountered an error: {e}")
        await worker.stop()

    logger.info(f"Evaluation worker {worker_id} stopped")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Agent Evaluation Tool Worker")
    parser.add_argument("--id", default="worker-1", help="Worker ID")
    parser.add_argument("--config", help="Path to custom configuration file (defaults to config/worker.yaml)")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    # Set default config path for worker if not specified
    config_path = args.config
    if not config_path:
        # Use worker.yaml as default
        worker_config = Path("config/worker.yaml")
        if worker_config.exists():
            config_path = str(worker_config)
        else:
            raise FileNotFoundError("Worker configuration file not found. Please ensure config/worker.yaml exists.")

    # Set environment variables
    if config_path:
        os.environ["CONFIG_PATH"] = config_path

    # Run the worker
    asyncio.run(run_worker(args.id, config_path))
