# Agent Evaluation Tool

一个轻量级、可扩展的大语言模型(LLM)智能体能力综合评估框架，集成了多种主流基准测试，包括tau-bench、Berkeley Function Calling Leaderboard-v3和GAIA（还未完成）。

## 📋 目录

- [项目特点](#项目特点)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
  - [环境要求](#环境要求)
  - [安装](#安装)
  - [配置](#配置)
  - [启动服务](#启动服务)
  - [配置文件说明](#配置文件说明)
- [调试](#调试)
- [API使用指南](#api使用指南)
  - [创建评估任务](#创建评估任务)
  - [批量评估多个模型](#批量评估多个模型)
  - [API参数说明](#api参数说明)
  - [查看任务状态](#查看任务状态)
  - [取消/删除任务](#取消删除任务)
  - [查看工作器统计信息](#查看工作器统计信息)
  - [查看所有任务](#查看所有任务)
  - [查看可用基准测试](#查看可用基准测试)
  - [查看评估结果](#查看评估结果)
- [基准测试集成](#基准测试集成)
  - [tau-bench](#tau-bench)
  - [Berkeley Function Calling Leaderboard-v3](#berkeley-function-calling-leaderboard-v3)
  - [GAIA](#gaia)
- [扩展指南](#扩展指南)
  - [添加新的基准测试](#添加新的基准测试)
  - [添加新的Agent框架](#添加新的agent框架)
- [项目结构](#项目结构)
- [常见问题](#常见问题)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 项目特点

- **轻量级架构**：基于FastAPI和异步工作器
- **多模型批量评估**：支持同时评估多个模型，高效利用资源
- **多工作器并行**：支持启动多个工作器，实现真正的并行处理
- **异步任务处理**：任务异步执行，提高评估效率
- **智能负载均衡**：任务自动分发到空闲工作器，优化资源利用
- **可扩展设计**：易于添加新的基准测试和评估方法
- **统一API接口**：所有LLM通过OpenAI兼容API访问，简化集成

## 系统架构

![系统架构](docs/architecture.png)

系统由以下主要组件构成：

- **API层**：基于FastAPI的RESTful API接口
- **任务队列**：基于异步队列的任务管理系统
- **评估工作器池**：处理评估任务的异步工作器
- **基准测试适配器**：连接不同基准测试的适配器
- **Agent框架集成**：集成AutoGen和SmolAgents等框架
- **LLM服务层**：统一的LLM访问接口

## 快速开始

### 环境要求

- Python 3.10
- 已部署的OpenAI兼容API服务（如vLLM）

### 安装

1. 克隆仓库：

```bash
git clone https://gitee.pjlab.org.cn/L1/zhudongsheng/agenteval.git
cd agenteval
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 设置基准测试：

基准测试代码已包含在 `external/` 目录中。根据需要安装依赖：

```bash
# 安装 tau-bench 依赖
pip install litellm pydantic

# 安装 BFC 依赖
cd external/gorilla/berkeley-function-call-leaderboard
pip install -e .
```

### 配置

1. 根据`.env.example`文件创建属于自己的`.env`。

2. 根据需要修改`config/server.yaml`和`config/worker.yaml`配置文件。

### 启动服务

1. 启动API服务：

```bash
python api_server.py
```

2. 启动工作器：

```bash
# 启动单个工作器
python evaluation_worker.py

# 启动多个工作器（推荐，提高并发处理能力）
python evaluation_worker.py --id worker-1 &
python evaluation_worker.py --id worker-2 &
python evaluation_worker.py --id worker-3 &
python evaluation_worker.py --id worker-4 &
```

默认情况下，API服务运行在`http://localhost:8000`。

### 配置文件说明

项目使用分离的配置文件：

- `config/server.yaml` - API服务器配置（端口、主机、队列设置等）
- `config/worker.yaml` - 工作器配置（基准测试、模型设置、评估参数等）
- `config/custom_server.yaml` - 自定义API服务器配置示例
- `config/custom_worker.yaml` - 自定义工作器配置示例

API服务器默认使用 `config/server.yaml`，工作器默认使用 `config/worker.yaml`。

## 🐛 调试

本项目提供了完整的调试环境，用于调试 FastAPI 和任务队列功能。

### 调试环境

项目提供了完整的VSCode调试配置：

```bash
# 环境检查
python debug/scripts/setup_debug.py

# 开始调试
code .  # 打开VSCode
# 按 Ctrl+Shift+D → 选择调试配置 → 按 F5
```

### 相关文档

- **[debug/DEBUG_GUIDE.md](debug/DEBUG_GUIDE.md)** - 完整的调试指南
- **[debug/DEBUG_README.md](debug/DEBUG_README.md)** - 快速调试入门

## 📚 API使用指南

### 创建评估任务

#### tau-bench 任务示例

```bash
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "tau_bench",
    "model": "Qwen/Qwen3-8B",
    "params": {
      "env": "retail",
      "agent_strategy": "tool-calling",
      "task_split": "test",
      "temperature": 0.95,
      "max_concurrency": 2
    }
  }'
```

#### BFC 任务示例

```bash
# function call 模式
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "bfc",
    "model": "Qwen/Qwen3-8B",
    "params": {
      "test_category": ["simple"],
      "temperature": 0.95,
      "use_fc_mode": true,
      "num_threads": 2
    }
  }'

# prompt 模式
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "bfc",
    "model": "gpt-4",
    "params": {
      "test_category": ["simple"],
      "temperature": 0.95,
      "use_fc_mode": false,
      "num_threads": 2
    }
  }'
```

### 批量评估多个模型

#### tau-bench 批量任务

```bash
curl -X POST "http://localhost:8000/api/tasks/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "tau_bench",
    "models": ["gpt-4", "gpt-3.5-turbo", "claude-3-opus"],
    "params": {
      "env": "retail",
      "task_split": "test",
      "temperature": 0.95,
      "max_concurrency": 2
    }
  }'
```

#### BFC 批量任务

```bash
curl -X POST "http://localhost:8000/api/tasks/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "bfc",
    "models": ["gpt-4", "gpt-3.5-turbo"],
    "params": {
      "test_category": ["all"],
      "temperature": 0.95,
      "num_threads": 2
    }
  }'
```

#### GAIA 任务

```bash
# 单个任务 - 指定并发数
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gaia",
    "model": "gpt-4",
    "params": {
      "level": 1,
      "num_tasks": 10,
      "max_concurrent_requests": 3,
      "request_timeout": 300
    }
  }'

# 批量任务 - 所有级别，高并发
curl -X POST "http://localhost:8000/api/tasks/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gaia",
    "models": ["gpt-4", "claude-3-opus"],
    "params": {
      "level": "all",
      "max_concurrent_requests": 8,
      "request_timeout": 600
    }
  }'
```

### API参数说明
目前 agenteval 的 benchmark 使用源码的参数名称，未做统一。建议先在 src/benchmark_config 了解各个参数的含义。以下是常用参数：

#### 通用参数
- `benchmark`: 基准测试名称 (`tau_bench`, `bfc`, `gaia`)
- `model`: 模型名称 (如 `gpt-4`, `gpt-3.5-turbo`, `claude-3-opus`)
- `params`: 基准测试特定参数

#### tau-bench 参数
- `params.env`: 环境类型 (`retail`, `airline`)，默认为 `retail`
- `params.task_split`: 任务分割 (`train`, `test`, `dev`)，默认为 `test`
- `params.temperature`: 模型温度参数
- `params.max_concurrency`: 最大并发任务数，默认为 `1`

#### BFC 参数
- `params.test_category`: 测试类别列表 (`["simple"]`, `["multiple"]`, `["parallel"]`, `["all"]`)，默认为 `["all"]`
- `params.temperature`: 模型温度参数
- `params.use_fc_mode`: 是否使用 function call 模式，默认为 `false`
  - `true`: 使用 function call 模式
  - `false`: 使用 prompt 模式
- `params.num_threads`: 并发线程数，默认为 `1`

#### GAIA 参数
- `params.level`: 任务难度级别 (`1`, `2`, `3`, `"all"`)，默认为 `None` (所有级别)
- `params.num_tasks`: 限制任务数量，默认为 `None` (所有任务)
- `params.max_concurrent_requests`: 最大并发请求数，默认为 `5`
- `params.service_url`: GAIA 服务 URL，默认为 `"http://localhost:8000"`
- `params.request_timeout`: 请求超时时间（秒），默认为 `600`


### 查看任务状态

```bash
# 系统会自动分配唯一的任务ID，用户无需指定内部任务索引。
curl -X GET "http://localhost:8000/api/tasks/{task_id}"
```

### 取消/删除任务

```bash
# 根据任务ID取消正在运行的任务或删除已完成的任务
curl -X DELETE "http://localhost:8000/api/tasks/{task_id}"
```

### 查看工作器统计信息

```bash
curl -X GET "http://localhost:8000/api/stats"
```

### 查看所有任务

```bash
curl -X GET "http://localhost:8000/api/tasks"
```

### 查看可用基准测试

```bash
curl -X GET "http://localhost:8000/api/benchmarks"
```

### 查看评估结果

```bash
curl -X GET "http://localhost:8000/api/results"
```


## 🧪 基准测试集成

### tau-bench

tau-bench是一个综合性的LLM评估基准，包含多种任务类型。本框架集成了tau-bench的评估逻辑。

**支持的环境类型**：
- `retail`: 零售客服场景任务
- `airline`: 航空客服场景任务

**可用的任务分割**：`train`, `test`, `dev`

### Berkeley Function Calling Leaderboard-v3

BFC-v3专注于评估LLM的函数调用能力，本框架复用了其官方评估代码。

**🆕 AgentEval集成特性**：
- **统一推理后端**: 使用AgentEval的inference backends替代原生model handlers
- **模式参数化**: 通过`use_fc_mode`参数控制函数调用vs提示模式
- **向后兼容**: 保持原有BFC评估逻辑完整性
- **自动后端选择**: 根据模型名称自动选择合适的推理后端

**支持的测试类别**：
- `simple`: 简单函数调用
- `multiple`: 多函数调用
- `parallel`: 并行函数调用
- `parallel_multiple`: 并行多函数调用
- `irrelevance`: 无关性检测
- `java`: Java函数调用
- `javascript`: JavaScript函数调用
- `live_simple`: 实时简单调用
- `live_multiple`: 实时多函数调用
- `live_parallel`: 实时并行调用
- `multi_turn_base`: 多轮对话基础
- `multi_turn_miss_func`: 多轮对话缺失函数
- `multi_turn_miss_param`: 多轮对话缺失参数
- `all`: 所有测试类别

**评估指标**：
- Overall Accuracy: 总体准确率
- AST Accuracy: 抽象语法树准确率
- Exec Accuracy: 执行准确率
- Relevance Detection: 相关性检测
- Irrelevance Detection: 无关性检测

### GAIA

GAIA是一个开放的基准测试，本框架通过AutoGen和SmolAgents等框架实现了GAIA任务的执行和评估。

## 🔧 扩展指南

### 添加新的基准测试

详细的基准测试集成指南请参考：**[基准测试集成开发指南](docs/benchmark_integration_guide.md)**

该指南包含：
- 完整的集成步骤和最佳实践
- 适配器开发模板和示例代码
- 推理后端集成方法
- 测试和调试技巧
- 故障排除和性能优化

**快速集成步骤**：
1. 在`src/benchmark_config`目录下创建配置类，继承`BaseBenchmarkConfig`
2. 在`src/adapters`目录下创建新的适配器类，继承`BaseAdapter`
3. 实现必要的方法：`execute`, `validate_params`
4. 在`src/adapters/registry.py`中注册新的适配器
5. 在`config/worker.yaml`中添加基准测试配置
6. 创建调试脚本进行测试

### 添加新的Agent框架

1. 在`src/frameworks`目录下创建新的框架包装器
2. 实现`execute_task`方法
3. 在GAIA适配器中集成新的框架

## 📁 项目结构

```
agent-eval/
├── config/                    # 配置文件目录
│   ├── default.yaml           # 默认配置
│   └── worker.yaml            # 工作器配置
├── src/                       # 源代码目录
│   ├── api/                   # API层
│   │   ├── routes.py          # FastAPI路由
│   │   └── models.py          # API请求/响应模型
│   ├── engine/                # 执行引擎
│   │   ├── queue.py           # 任务队列实现
│   │   ├── worker.py          # 评估工作器
│   │   ├── config.py          # 配置管理
│   │   └── logging_config.py  # 日志配置
│   ├── inference_backends/    # 推理后端系统
│   │   ├── manager.py         # 后端管理器
│   │   ├── openai_backend.py  # OpenAI后端
│   │   ├── anthropic_backend.py # Anthropic后端
│   │   ├── local_backend.py   # 本地后端
│   │   └── google_backend.py  # Google后端
│   ├── benchmark_config/      # 基准测试配置类
│   │   ├── base.py            # 基础配置类
│   │   ├── tau_bench.py       # Tau-Bench配置
│   │   ├── bfc.py             # BFC配置
│   │   └── gaia.py            # GAIA配置
│   ├── adapters/              # 基准测试适配器
│   │   ├── base.py            # 基础适配器接口
│   │   ├── registry.py        # 适配器注册表
│   │   ├── tau_bench.py       # Tau-Bench适配器
│   │   ├── bfc.py             # Berkeley Function Calling适配器
│   │   └── gaia.py            # GAIA适配器
│   ├── frameworks/            # Agent框架集成
│   │   ├── autogen_wrapper.py # AutoGen包装器
│   │   └── smol_wrapper.py    # SmolAgents包装器
│   └── storage/               # 存储实现
│       └── db.py              # 数据库接口
├── docs/                      # 文档目录
│   ├── benchmark_integration_guide.md  # 集成指南
│   ├── system_architecture.md          # 系统架构
│   └── parameter_validation_architecture.md # 参数验证架构
├── external/                  # 外部基准测试代码
│   ├── tau-bench/             # tau-bench代码
│   ├── gorilla/               # Gorilla项目代码
│   │   └── berkeley-function-call-leaderboard/  # BFC代码
│   └── gaia-tasks/            # GAIA任务定义
├── debug/                     # 调试脚本
│   └── scripts/               # 调试脚本目录
├── api_server.py              # API服务入口
├── evaluation_worker.py       # 工作器入口
├── setup_benchmarks.py        # 基准测试设置脚本
└── requirements.txt           # 依赖列表
```

## ❓ 常见问题

### 如何调整并发任务数量？

在`config/worker.yaml`中修改`worker.max_concurrent_tasks`和`worker.model_concurrency`设置。

**配置示例**：
```yaml
worker:
  max_concurrent_tasks: 5  # 每个工作器最大并发任务数
  model_concurrency:
    gpt-4: 2              # 每个工作器最多2个GPT-4并发
    gpt-3.5-turbo: 5      # 每个工作器最多5个GPT-3.5并发
```

**总并发计算**：如果启动4个工作器，总的GPT-4并发数 = 4 × 2 = 8个

### 如何添加自定义任务？

对于GAIA基准测试，可以在`external/gaia-tasks`目录下添加新的任务定义JSON文件。

### 如何使用自定义LLM模型？

在`config/worker.yaml`的`llm.endpoints`部分添加新的模型端点配置。

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循以下步骤：

1. Fork仓库
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

[MIT License](LICENSE)
