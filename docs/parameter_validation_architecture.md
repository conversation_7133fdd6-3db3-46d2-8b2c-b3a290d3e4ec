# 参数验证架构

## 概述

本文档描述了Agent Evaluation Tool中参数验证的统一架构，解决了之前存在的代码冗余和验证逻辑分散的问题。

## 架构原则

### 单一职责原则
- **API层** (`src/api/models.py`): 只负责基本的请求格式验证（如必需字段存在）
- **配置层** (`src/benchmark_config/`): 负责所有参数的详细验证、类型检查和默认值设置
- **适配器层** (`src/adapters/`): 负责运行时特定的验证（如模型可用性、测试类别有效性）

### 避免重复验证
- 移除了API层中硬编码的基准测试特定验证逻辑
- 所有参数验证统一在benchmark_config类中处理
- 适配器只进行需要运行时信息的额外验证

## 实现细节

### 1. API层简化

**之前的问题:**
```python
# src/api/models.py - 硬编码的验证逻辑
@model_validator(mode='after')
def validate_benchmark_params(self):
    if self.benchmark == "tau_bench":
        env = self.params.get("env")
        if env and env not in ["retail", "airline"]:
            raise ValueError("params.env must be 'retail' or 'airline' for tau_bench")
    return self
```

**改进后:**
```python
# src/api/models.py - 简化的验证逻辑
@model_validator(mode='after')
def validate_benchmark_params(self):
    if not self.params:
        self.params = {}
    # Parameter validation is handled by benchmark_config classes in adapters
    # This ensures consistency and avoids duplication
    return self
```

### 2. 配置层统一

每个基准测试都有对应的配置类，继承自`BaseBenchmarkConfig`:

```python
# src/benchmark_config/tau_bench.py
class TauBenchConfig(BaseBenchmarkConfig):
    env: str = Field(default="retail", description="Environment to run tasks in")
    
    @field_validator('env')
    @classmethod
    def validate_env(cls, v: str) -> str:
        valid_envs = ["retail", "airline"]
        if v not in valid_envs:
            raise ValueError(f"Invalid env: {v}. Must be one of {valid_envs}")
        return v
```

### 3. 适配器层简化

**之前的问题:**
```python
# 冗余的验证逻辑
if "model" not in params:
    raise ValueError("Missing required parameter: model")

# 重复的参数检查
if self.benchmark == "tau_bench":
    env = self.params.get("env")
    if env and env not in ["retail", "airline"]:
        raise ValueError("params.env must be 'retail' or 'airline' for tau_bench")
```

**改进后:**
```python
# src/adapters/tau_bench.py - 简化的验证逻辑
async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
    from src.core.benchmark_config.tau_bench import TauBenchConfig
    
    try:
        # Create TauBenchConfig - this handles all validation, normalization, and defaults
        tau_config = TauBenchConfig(**params)
        
        # Return the validated parameters as a dictionary
        validated_params = tau_config.model_dump()
        # Remove runtime-only fields
        for field in ["inference_backend", "api_key", "base_url"]:
            validated_params.pop(field, None)
        return validated_params
    
    except Exception as e:
        raise ValueError(f"Parameter validation failed: {str(e)}")
```

## 优势

### 1. 消除代码重复
- 参数验证逻辑只在一个地方定义（benchmark_config）
- API层不再需要硬编码特定基准测试的验证规则

### 2. 提高可维护性
- 新增参数只需要在配置类中定义
- 验证规则的修改只需要在一个地方进行

### 3. 增强类型安全
- 使用Pydantic提供强类型检查
- 自动生成参数文档和默认值

### 4. 更好的扩展性
- 新基准测试只需要创建对应的配置类
- API层无需修改即可支持新的基准测试参数

## 配置类结构

所有配置类都继承自`BaseBenchmarkConfig`:

```python
# src/benchmark_config/base.py
class BaseBenchmarkConfig(BaseModel):
    model: str
    inference_backend: Optional[Any] = None
    
    class Config:
        arbitrary_types_allowed = True
```

当前支持的配置类:
- `TauBenchConfig`: Tau-Bench基准测试配置
- `BFCConfig`: Berkeley Function Calling基准测试配置  
- `GAIAConfig`: GAIA基准测试配置

## 验证流程

1. **API请求** → `TaskRequest`/`BatchTaskRequest` (基本格式验证)
2. **适配器调用** → `validate_params()` 方法
3. **配置类创建** → `BenchmarkConfig(**params)` (详细参数验证)
4. **运行时验证** → 适配器特定的额外检查（如模型可用性）
5. **返回验证结果** → 标准化的参数字典

这种架构确保了参数验证的一致性、可维护性和扩展性。
