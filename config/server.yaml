# Server configuration for Agent Evaluation Tool API Server
# This configuration contains only settings needed to run the API server

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  debug: false

# Task Queue Configuration
queue:
  max_size: 100
  worker_count: 4  # This is for display purposes only; actual workers run separately

# Storage Configuration (for task status and results retrieval)
storage:
  type: "json"
  path: "data/results"  # Optional, specify JSON file storage directory
#   type: "sqlite"
#   path: "data/results.db"  # Optional, specify database file storage directory

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: "./logs/api_server.log"
  max_size_mb: 10
  backup_count: 5
