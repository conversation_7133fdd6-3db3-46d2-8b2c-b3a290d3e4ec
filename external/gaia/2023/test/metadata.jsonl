{"task_id": "6af95c8f-8cbf-4c12-b02c-f9a23cc1ecb9", "Question": "Here's a fun riddle that I'd like you to try.\n\nAn adventurer exploring an ancient tomb came across a horde of gold coins, all neatly stacked in columns. As he reached to scoop them into his backpack, a mysterious voice filled the room. \"You have fallen for my trap adventurer,\" the voice began, and suddenly the doorway to the chamber was sealed by a heavy rolling disk of stone. The adventurer tried to move the stone disk but was unable to budge the heavy stone. Trapped, he was startled when the voice again spoke. \n\n\"If you solve my riddle, I will reward you with a portion of my riches, but if you are not clever, you will never leave this treasure chamber. Before you are 200 gold coins. I pose a challenge to you, adventurer. Within these stacks of coins, all but 30 are face-up. You must divide the coins into two piles, one is yours, and one is mine. You may place as many coins as you like in either pile. You may flip any coins over, but you may not balance any coins on their edges. For every face-down coin in your pile, you will be rewarded with two gold coins. But be warned, if both piles do not contain the same number of face-down coins, the door will remain sealed for all eternity!\"\n\nThe adventurer smiled, as this would be an easy task. All he had to do was flip over every coin so it was face down, and he would win the entire treasure! As he moved to the columns of coins, however, the light suddenly faded, and he was left in total darkness. The adventurer reached forward and picked up one of the coins, and was shocked when he realized that both sides felt almost the same. Without the light, he was unable to determine which side of the coin was heads and which side was tails. He carefully replaced the coin in its original orientation and tried to think of a way to solve the puzzle. Finally, out of desperation, the adventurer removed 30 coins to create his pile. He then carefully flipped over each coin in his pile, so its orientation was inverted from its original state.\n\n\"I've finished,\" he said, and the lights returned. Looking at the two piles, he noticed that the larger pile contained 14 face-down coins.\n\nWhat was the outcome for the adventurer? If he failed the challenge, please respond with \"The adventurer died.\" Otherwise, please provide the number of coins the adventurer won at the conclusion of the riddle. If the adventurer won any coins, provide your response as the number of coins, with no other text.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c80ed443-b494-4e86-bec8-10ecb41c2326", "Question": "What animal is shown on page 54 of Anatomy and Physiology of Animals by Ruth Lawson in Dirk H\u00fcnniger's 2015 pdf conversion? Just give the common name, without any article.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e14448e9-5243-4b07-86e1-22e657f96bcf", "Question": "On the Wikipedia page for the animal in the provided image, how many revisions from before 2020 had \"visual edit\" tags?", "Level": 3, "file_name": "e14448e9-5243-4b07-86e1-22e657f96bcf.jpg", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "198ffd8f-6041-458d-bacc-fe49872cfa43", "Question": "In the story I've attached with this message, the protagonist describes rescuing a family member of an important noble, which resulted in the protagonist earning a commission as a lieutenant. How many foes did the protagonist defeat during the rescue, earning the protagonist the rank?", "Level": 1, "file_name": "198ffd8f-6041-458d-bacc-fe49872cfa43.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6583799b-573a-4e95-8b28-4f0397bd45c2", "Question": "In what year did someone first edit the english Wikipedia page for horror movie character Michael Myers on the day of Halloween?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "12a682d7-8e8e-4d4c-8102-a97628027441", "Question": "The Manx Electric Railway operates trams on the Isle of Man. How many switches does a tram traveling from Groudle Glen to South Cape pass through? Disregard any changes to the track made after June 2023.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4044eab7-1282-42bd-a559-3bf3a4d5858e", "Question": "The attached file shows a library\u2019s collection of books and films. What is the ISBN number of the book in the Hunger Games series that is currently checked out? Return the ISBN as it appears on Wikipedia in July of 2023.", "Level": 2, "file_name": "4044eab7-1282-42bd-a559-3bf3a4d5858e.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6ace0798-0d0f-4122-8705-78acebf23191", "Question": "Take the volume of Apple shares traded April 6 1987 and divide it by the shares traded June 14 2021. Round to one decimal place.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "11016eca-0f56-443b-985e-a927bd4fc1b0", "Question": "What is the total weight in grams of all ingredients measured in grams or milliliters in the 2021 The Guardian recipe from the author of Plenty for the food that shares a name with a young Carl E. Schultze, given that chicken stock is 5 times as dense as water and stopping at the rolls it's served in?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7b10295b-5287-41d7-897c-663fe194c04d", "Question": "If you use some of the letters in the given Letter Bank to spell out the sentence \"I am a penguin halfway to the moon\", which of the remaining unused letters would have to be changed to spell out, \"The moon is made of cheese\"? Return a comma-separated alphabetized list.\nLetter Bank: {OAMFETIMPECRFSHTDNIWANEPNOFAAIYOOMGUTNAHHLNEHCME}", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4033181f-1988-476b-bc33-6da0f96d7bd0", "Question": "I was reading this book last year, and it had this really cool description where it referred to something as looking like a manta ray. I\u2019m trying to figure out when I read that part, but I can\u2019t find what book it\u2019s from. This file I attached has a list of the books I read last year, with the date I started and finished reading each one. I\u2019d like for you to tell me the month in which I likely read the passage about the manta ray.", "Level": 2, "file_name": "4033181f-1988-476b-bc33-6da0f96d7bd0.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7707f3dd-1aa6-42f5-847a-b66f3eaf2ee4", "Question": "I'd like you to review the attached image. The image contains different shapes of varied colors. Each shape is numbered with a black number located inside the boundary of the shape. Please identify the blue shape with the smallest area. Limit your response to the number associated with the smallest blue shape.", "Level": 2, "file_name": "7707f3dd-1aa6-42f5-847a-b66f3eaf2ee4.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5f862c5d-1d51-4efa-bbd0-eb313579b5f8", "Question": "What was the net byte change in the Wikipedia page for the top ten largest additions or removals from the page of the third most populous penguin species as of July 2023?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cf0682db-1e31-4a36-913f-aedd501e44d1", "Question": "A data annotator stayed up too late creating test questions to check that a system was working properly and submitted several questions with mathematical errors. On nights when they created 15 test questions, they made 1 error. On nights when they created fewer than 15 questions, they also corrected 3 errors. On nights they created 20 questions, they made 0 errors. On nights when they created 25 or more, they made 4 errors. Over the course of five nights, the worker produced a total of 6 errors. When asked how many nights they created 15 questions, they gave three possible numbers as responses. What are the three numbers, presented in the format x, y, z in ascending order?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "17a9628e-ccef-433c-9109-acaf024d5a55", "Question": "I was reading a novel a while ago. It was one of Frank Belknap Long's stories. In the story, the protagonist starts off beating up a security guard, in either the first or second chapter. And by the end, he ends up saving the day by piloting some sort of aircraft and blowing up the headquarters of an oppressive regime. He does it by infiltrating a group of the aircraft, although I don't remember if it really says how he gets in the aircraft. Anyway, could you please tell me how many aircraft were in the initial group, before the protagonist started to fight back and shoot the others down? Just an integer number as your answer is fine.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "07ed8ebc-535a-4c2f-9677-3e434a08f7fd", "Question": "Please solve the following crossword:\n\n|1|2|3|4|5|\n|6| | | | |\n|7| | | | |\n|8| | | | |\n|X|9| | | |\n\nI have indicated by numbers where the hints start, so you should replace numbers and spaces by the answers.\nAnd X denotes a black square that isn\u2019t to fill.\n\nACROSS\n- 1 Wooden strips on a bed frame\n- 6 _ Minhaj, Peabody-winning comedian for \"Patriot Act\"\n- 7 Japanese city of 2.6+ million\n- 8 Stopwatch, e.g.\n- 9 Pain in the neck\n\nDOWN\n- 1 Quick drink of whiskey\n- 2 Eye procedure\n- 3 \"Same here,\" in a three-word phrase\n- 4 Already occupied, as a seat\n- 5 Sarcastically critical commentary. Answer by concatenating the characters you choose to fill the crossword, in row-major order.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cd886ddd-2d12-4347-9c7a-64774f66a3d3", "Question": "In the reference file, there is a column of five numbers and a column of five letters. Imagine a straight line that starts from the first number and ends at the last letter. Then imagine another straight line that starts from the last number and ends at the first letter. Now imagine another line that starts from the second number and ends at the second letter. Finally, imagine another straight line that starts from the fourth number to the fourth letter. How many intersections are formed by these imaginary lines? Express your answer using a numerical value.", "Level": 2, "file_name": "cd886ddd-2d12-4347-9c7a-64774f66a3d3.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d89733a3-7d86-4ed8-b5a3-bf4831b06e3c", "Question": "If hopping over the cylinder in this photo skips three steps and I take two steps at a time, how many steps do I need to take to reach the top? Don't consider the hop as a step.", "Level": 1, "file_name": "d89733a3-7d86-4ed8-b5a3-bf4831b06e3c.jpg", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ac8e6d96-57a8-4936-a910-2bb04794b041", "Question": "What brand of webcam is used on the laptop embedded in the cupola of the ISS?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cfd773c8-371f-425c-b081-f254f96c0530", "Question": "The attached file lists locomotives owned by a local railroad museum. What is the ID number of the steam locomotive being exhibited in the Display Hall?", "Level": 1, "file_name": "cfd773c8-371f-425c-b081-f254f96c0530.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "862862cb-519c-492a-8bee-309f3a8a5191", "Question": "In the facebookresearch/dinov2 github repo, what was the number of heads of the biggest default transformer architecture in the original commit?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c7d40b93-4ee9-4643-8a25-1bb1d5c00f06", "Question": "I\u2019m making a 1:48 scale model of the Space Mountain roller coaster at Magic Kingdom. I need to know the following measurements from english wikipedia as of July 2023:\n\n- Track height at tallest point\n- Track length\n- Height of the tallest drop\n\nI\u2019d like you to tell me what those measurements are, in inches, in 1:48 scale. Separate the measurements by commas and give it in the same order as above. If a roller coaster has two tracks, I usually use two separate sets of measurements, with a semicolon to mark the boundary between the sets. Place spaces between punctuation marks.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ca0a4c14-4b97-43e7-8923-539d61050ae3", "Question": "The attached file shows books in the collection of the Scribe County Public Library. One of the series in this collection stars a main character made out of beads. What percentage of the books the library has of that series are currently overdue?", "Level": 2, "file_name": "ca0a4c14-4b97-43e7-8923-539d61050ae3.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "15c0b8fe-d3be-45df-a759-13a488d4baaa", "Question": "How many cumulative milliliters of fluid is in all the opaque-capped vials without stickers in the 114 version of the kit that was used for the PromethION long-read sequencing in the paper De Novo-Whole Genome Assembly of the Roborovski Dwarf Hamster (Phodopus roborovskii) Genome?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fcf5b93d-27fd-4637-ac5d-fad4e445ca94", "Question": "I wanted to make another batch of cherry melomel. I remember liking the last recipe I tried, but I can't remember it off the top of my head. It was from the Reddit, r/mead. I remember that the user who made it had a really distinct name, I think it was StormBeforeDawn. Could you please look up the recipe for me? I'm not sure if it has been changed, so please make sure that the recipe you review wasn't updated after July 14, 2022. That's the last time I tried the recipe.\n\nWhat I want to know is how many cherries I'm supposed to use. I'm making a 10-gallon batch in two 5-gallon carboys. Please just respond with the integer number of pounds of whole cherries with pits that are supposed to be used for a 10-gallon batch.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fece4d7a-4093-47b8-b58c-68ec591ec4c8", "Question": "Verify each of the following ISBN 13 numbers:\n\n1. 9783518188156\n2. 9788476540746\n3. 9788415091004\n4. 9788256014590\n5. 9782046407331\n\nIf any are invalid, correct them by changing the final digit. Then, return the list, comma separated, in the same order as in the question.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "460ef201-c5f4-41f4-9acd-e4215384e678", "Question": "From January 2017 to January 2019 (included), how many edits made to the english wikipedia page of the Australian Aboriginal Flag mention vandalism? ", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7dc97fbf-c452-4b5d-9d19-db275daccd39", "Question": "According to the US Census Bureau website, what was the difference in population between the two states that have both Carl's Jr. and Hardee's fast food restaurants in the nearest census to the year the chains were joined?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "adfc0afb-3ef2-4e79-b66b-c49823b6b913", "Question": "As of Christmas 2022, the English language Wikipedia page for my favorite board game, Diplomacy, had four images. Please review the images, and count the number of human characters depicted in those images. For the purpose of counting humans, please only include those images that clearly show a human character. I want the number only. For example, a photograph or illustration that shows an individual human would count as one human character, but any illustrations or photographs of game tokens or other abstract representations used in the game to represent humans wouldn't be counted.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "68ff53a8-e1a1-42b0-9180-3c0a37187387", "Question": "In 2023, what animal was mentioned in vandalism that was removed by an edit tagged \"reverted\" on the Young Adult Fiction Wikipedia page? Use the plural form, no article.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "48f56def-d539-4780-a3f5-591a925a88bf", "Question": "Suppose a Titan RTX is exactly half as performant as an Nvidia RTX A6000 when batch processing 224x224 sized RGB image inputs. Given this, if Wong et al. had conducted their evaluations using a Titan RTX, what would have been the image throughput when using TurboViT for an image batch size of 32 images? Report the throughput as an integer, rounded to the nearest number of images per second.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c4456885-2f03-436f-8fe9-0b4ca6822cdb", "Question": "The attached file shows titles in the collection of Scribe County Public Library. Several of the films are adaptations of books that are also in its collection. Of these films, find the one that was released the soonest after the book it was based on was first published. Tell me what is listed under the \u201cStatus\u201d column for that film. Note that some books may have been released earlier than the films, but the film may not be based on it.", "Level": 3, "file_name": "c4456885-2f03-436f-8fe9-0b4ca6822cdb.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fa7bc619-bf17-4ef7-be80-c0e32144140f", "Question": "A porterhouse by any other name is centered around a letter. What does Three Dog Night think about the first natural number that starts with that letter? Give the first line from the lyrics that references it.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e20d0329-72bc-4634-ab37-47a217c2cfb6", "Question": "On the National Gallery of Art Conservation publication page for the volume about the artist famous for painting ballet dancers, how many years were between the first year mentioned in the captions of the first and last paintings shown that have more than one person in them?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7674ee67-d671-462f-9e51-129944749a0a", "Question": "The opponent of the player who has the grid in the attached image file calls out the first move made in Game 10 of the World Chess Championship title match won by Bobby Fischer, using algebraic notation. What is the name of the game piece into which the player will have to put a red peg as a result, according to the 1990 Milton Bradley rules for the game? Answer without articles.", "Level": 3, "file_name": "7674ee67-d671-462f-9e51-129944749a0a.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8e41f740-f193-4540-a7b1-62e0f4b804a0", "Question": "Bob has genome type Aa, and Linda has genome type Aa. Assuming that a child of theirs also has a child with someone who also has genome type Aa, what is the probability that Bob and Linda's grandchild will have Genome type Aa? Write the answer as a percentage, rounding to the nearest integer if necessary.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "78cfc686-0e24-4e8e-acb4-291198301286", "Question": "A Pokemon trainer has the following six Pokemon on his team: Aipom, Buizel, Ekans, Electabuzz, Espurr, and Vulpix. The Vulpix is a newborn. If the trainer evolves all six of these Pokemon to their next stage, how many more tails will his team have in total compared to now?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ec9cb96f-0499-449e-b8f4-3eff8663ddd4", "Question": "According to the CDC website, based on the year with the highest number of foodborne outbreaks selected for reports or investigations of listeria, E. coli, salmonella, and campylobacteria, which year between 2011 and 2021 (inclusive) was the most dangerous to eat food relative to these illnesses?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c7003252-fc58-44bf-92f5-ec3991a49d00", "Question": "The attached spreadsheet lists contact information for employees of the consulting company Great Ape Solutions. How many employees appear to be next-door neighbors of another employee? Assume that houses next to each other have consecutive addresses, and don\u2019t include people who live in the same building as next-door neighbors.", "Level": 1, "file_name": "c7003252-fc58-44bf-92f5-ec3991a49d00.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6e343c42-7f09-4df5-aab9-22c0293c2d30", "Question": "Find the Fall/Winter 2019 issue of \u201cThe Lamp-Post of the Southern California C.S. Lewis Society.\u201d In the article that begins on the 46th page, what does the author state that both Lewis and Jung believe was the originator of archetypes?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2e958b48-d4bf-4dc5-b051-442a2ef70630", "Question": "In a Gamespot review for a video game based on the movie whose set was later reused by the TV show Stargate: Atlantis, a fast food restaurant was mentioned. According to SEC documents, what was that restaurant's average number of employees in the world in the year the review was written? Just give the number.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3f9f12e1-c848-4925-afea-eb484e17e986", "Question": "Which regional state has the most woredas according to the 2007 Population and Housing Census of Ethiopia? Give its name without appending region or state.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "640e86a8-a261-4f6a-b507-0f510484eeff", "Question": "In the release of the version of SciPy where the hyp2f0 function was deprecated, how many of the issues closed end in a question mark?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "35320d83-246a-4367-8ed8-c04a93c68daa", "Question": "I\u2019m curious about the Wikipedia page for the biblical town of Emmaus. How many pictures were on the oldest revision of the page?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "880c9d7d-aeb1-4fb0-a389-b9a88c451b50", "Question": "I was listening to my favorite podcast a few months ago, the Joe Rogan Experience. The guest was Duncan Trussell, who is my favorite guest. I know they were talking about ChatGPT, and they mentioned a method of subverting ChatGPT. I can't remember what they called it though. It was either a three-letter acronym, or maybe a person's name. Look that up for me and tell me what that acronym or name was. Also, today is June 8, 2023, and I'm talking about the most recent episode that had Duncan Trussell as the guest. Your answer should be in the format: A.B.C.D., Alpha Bravo Charlie Delta. In other words, you specify in a numbered list: first, the acronym (separated by period), second what the acronym stands for.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8f697523-6988-4c4f-8d72-760a45681f68", "Question": "How many characters in the attached document are numbers, quotation marks, apostrophes, or the particular punctuation symbol present in the Yahoo logo?", "Level": 2, "file_name": "8f697523-6988-4c4f-8d72-760a45681f68.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7556cbdb-c736-4efa-b3ef-2ebc2c8f4da3", "Question": "An array of candy is set out to choose from including gumballs, candy corn, gumdrops, banana taffy, chocolate chips, and gummy bears. There is one bag of each type of candy. The gumballs come in red, orange, yellow, green, blue, and brown. The candy corn is yellow, white, and orange. The gumdrops are red, green, purple, yellow, and orange. The banana taffy is yellow. The chocolate chips are brown and white. The gummy bears are red, green, yellow, and orange. Five people pass through and each selects one bag. The first selects one with only primary colors. The second selects one with no primary colors. The third selects one with all the primary colors. The fourth selects one that has neither the most nor the least colors of the remaining bags. The fifth selects the one with their favorite color, green. A second bag of the candy the first person chose is added to the remaining bag of candy. Which two candies are in the remaining bag after the addition? Give me them in a comma separated list, in alphabetical order", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c265a892-f429-454a-bfc1-d2164b520a90", "Question": "According to the 2011 Indian census, how many rural men in the Tamil Nadu census area stated they were Scheduled Caste (what was previously referred to as Dalit) with the name Panchama?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "a0dcc222-691e-4b03-ac75-c4493991ab80", "Question": "The attached .txt file is an export file from the competitive Pok\u00e9mon application Pok\u00e9mon Showdown, representing a team of six Pok\u00e9mon characters. Out of all the moves that Pok\u00e9mon on that team have, what is the name of the move that has the highest base damage in the Generation 4 games (e.g. Pok\u00e9mon Diamond and Pok\u00e9mon Pearl)?", "Level": 2, "file_name": "a0dcc222-691e-4b03-ac75-c4493991ab80.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "02d0c7de-3cb6-43db-8b42-bb5be0923e28", "Question": "Find the antipodal coordinates of -48.8583701, -177.7077074. Go to google maps street view and face the tallest structure visible. Which direction are you facing?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5b5e7996-4de6-46c4-8be2-0943417886e5", "Question": "In the year 2020, where were koi fish found in the watershed with the id 02040203? Give only the name of the pond, lake, or stream where the fish were found, and not the name of the city or county.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f5d0b1c6-5e15-4c55-b60c-9fc855dda5cf", "Question": "The attached file shows the inventory of a movie and video game rental store. The company offers a seven-day rental for each title. If a customer keeps a title beyond that, it is marked as overdue. If a customer keeps a title for a month beyond the seven-day rental, it is marked as missing. How many horror titles are currently being kept by customers beyond the seven-day period?", "Level": 2, "file_name": "f5d0b1c6-5e15-4c55-b60c-9fc855dda5cf.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c134398d-e1eb-4dab-97a1-6d9f715e53d0", "Question": "Determine what the final digit of these 9 numbers would be under ISBN-10 standards:\n\n1. *********\n2. *********\n3. *********\n4. *********\n5. *********\n6. *********\n7. *********\n8. *********\n9. *********\n\nNow, create a new 9 digit number from their check digits in the order ********* where each digit here represents the check digit of the number in the list above associated with that list item number. So the first number should be the check digit for the fifth item (*********), the second for the 8th item (*********), the third the 7th item (*********), so on and so forth. If the check digit would be X, use 0 in its place.\n\nProvide the check digit under ISBN-10 standards for this new number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6f7d28c6-6a8c-44b2-8ea9-0988c1096a2a", "Question": "In Sonia Sanchez\u2019s poem \u201cfather\u2019s voice\u201d, what primary colour is evoked by the imagery in the beginning of the tenth stanza? Answer with a capitalized word.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "07f6a90e-ee1d-4d2b-ae1d-3556d66a9e11", "Question": "Of the nations that medalled in archery at the 2016 Summer Olympics, which one has the highest ratio of medals won in archery to total medals won at the 2016 Summer Olympics? Give the designated name used by the nation to participate in the Olympic Games.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "a4a37f5c-b160-43ed-8484-fe74572cb2c1", "Question": "In the version of the Australian Water Act 2007 created by amendment approximately a decade after its adoption, several changes were made to the previous version. What is the previous version of the line that was updated by Change #27 of Schedule 1 Part 1, from newline to newline? Answer without punctuation.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5b89b147-cdab-40e1-be5b-819bc076c270", "Question": "What is the name of the song that starts playing around the 2-minute mark in this file I attached? I just need the name of the song, not the artist or any additional information.", "Level": 1, "file_name": "5b89b147-cdab-40e1-be5b-819bc076c270.mp3", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "07d803c0-7975-4da2-a62b-c8abafcdad9d", "Question": "According to Papers with Code, what was the name of the first model to go beyond 70% of accuracy on ImageNet ?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "411e5d0e-52db-492f-97a3-bd93658c0357", "Question": "Could you please listen to the song \"Troubled Soul\" by Dan Reeder and tell me what instrument he's playing? Here's a link to the song so you can listen to it: https://www.youtube.com/watch?v=NaKIiBMgg_M", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5e76cfc3-f33d-4416-b8ae-3ef292830207", "Question": "What is the dimension of the boundary of the tame twindragon rounded to two decimal places?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9ace56cc-2454-4de2-8e39-d8fec7be6f44", "Question": "The Wikipedia user who edited the pages of Farl, Giger Bar, Helmcken Falls, Peyto Lake, and Sikorsky S-76 between the years of 2004 and 2008 inclusive is a member of what Wikipedia unit, according to their user page as of 2022? Answer without using articles.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f94d6a97-9825-4308-8c69-fe6be4f6dff5", "Question": "In what year was the home village of the subject of British Museum item #Bb,11.118 founded?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "06d24738-d12e-484a-999a-b6119f4167f4", "Question": "What's the top non-plant-based food on the potassium and energy table in the 2020 guide that the NIH potassium page updated the link to in March 2021?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2102c078-d0f8-4a66-91d7-caca752bf55b", "Question": "What is the ISSN of the journal that included G. Scott's potato article that mentioned both a fast food restaurant and a Chinese politician in the title in a 2012 issue?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "965a4ea3-bb77-4ddd-9ee6-3160a6f8b8ef", "Question": "What is the chart key label for the outermost color of the plotted points at the 10 measurement lines in Figure 3 of the first PDF cited on the Wikipedia page for Astatine as of 2022?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "269f42c1-d767-4426-baf3-0d91c4423177", "Question": "VNV Nation has a song that shares its title with the nickname of Louis XV. What album was it released with?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4e4b3e43-7240-47b2-8594-8dacd70ee12e", "Question": "What is the only ingredient that appears in its entirety on the third line of the ingredients list of the original 2017 label of the Trader Joe's Everything But The Bagel seasoning blend jar?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c3c656c0-b288-476d-85d6-6d7a95ad19ef", "Question": " As of the end of May 2023, of the states that have an English language percentage listed in their english wikipedia page infobox , which two had the closest percentages? Return your answer in alphabetical order and comma separated.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d6f433d5-4203-47d4-990a-28dbf99e6afd", "Question": "The article that begins on page 807 of the book with the doi 10.1353/mfs.2005.0004 begins with the author describing a train trip. In 2016, what was the population of the city, rounded to the nearest ten thousand, that the author\u2019s train was traveling to?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cca708f8-277f-4d9c-920c-26e8065b82ad", "Question": "In Japanese, the particle \u3092 is used in conjunction with a noun. Taking that function and applying it to the language of Esperanto, what would be the last three letters of a plural noun in Esperanto?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "99b5ea36-0310-49c4-85d8-9ae83a96029a", "Question": "What is the first replica spoken by the character who made the famous quote in this audio clip? Just give the second line of the replica.", "Level": 2, "file_name": "99b5ea36-0310-49c4-85d8-9ae83a96029a.m4a", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "35a5c13a-6012-461f-abfc-cde9e5a0a0dd", "Question": "Consider these two github versions of pytorch: 1.12.1 and 1.13. Consider the following inputs: a = torch.tensor([4.0, -5.0]), b = torch.tensor([2.0, 2.0]). What is the sum of the elements of the tensor resulting from the subtraction of the 1.13 version from the 1.12.1 version of torch.floor_divide(a, b)?\n", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "83692c1b-eab1-49f1-9ef4-151708310689", "Question": "As of July 2023, in the documentation for the Python library that replaced Theano according to its Github readme, how many methods in the final version of the class for backend C Types implementation have arguments other than only self, name, and/or sub?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2bb16c35-403a-4d4c-859e-a88ccd55f876", "Question": "In this academic paper, in a bar of chocolate that had the percentage makeup of the literature data's milk chocolate, what were the percentages of non-fiber carbohydrates measured along with the percentage of protein and percentage of fat to find the non-fiber carbohydrate content if they had a 2:1 ratio in the order they appeared in the paper, given in that same order in the answer as X, Y (without % sign)?", "Level": 2, "file_name": "2bb16c35-403a-4d4c-859e-a88ccd55f876.xml", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3157eb2e-2b7c-48ae-8676-437c64d813d5", "Question": "If I combine a Beatle's first name and a type of beer, in what category and year of Nobel Prize do I have a winner? Answer using the format CATEGORY, YEAR.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c68c0db6-1929-4194-8602-56dce5ddbd29", "Question": "What was the full name of the Speaker of the House of the U.S. Congress that passed the act in this document that appears under the effective date of a change of name amendment?", "Level": 3, "file_name": "c68c0db6-1929-4194-8602-56dce5ddbd29.xml", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c1e3311d-5112-4690-bcdc-7da181657835", "Question": "In the version of NumPy where the numpy.msort function was deprecated, which attribute was added to the numpy.polynomial package's polynomial classes?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "335024e1-aa6a-4a48-bba5-fad4ebd8868c", "Question": "The author whose work was found in both the \"Shadows Beneath\" anthology and in the winning anthology for the 2014 World Fantasy Award for Best Anthology had a roommate in college who became famous. As of the end of 2022, what percentage of the author's most successful crowdfunding campaign did his former roommate's longest winning streak make according to Wikipedia, rounded down to the nearest hundredth?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "55aeb0ce-9170-4959-befb-59f6116887a4", "Question": "A word meaning dramatic or theatrical forms a species of duck when appended with two letters and then duplicated. What is that word?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2db1003c-96b9-4e26-9223-5a2d491d26ac", "Question": "In Google\u2019s June 2011 Street View of 21\u00b035'34.1\"N 158\u00b006'12.2\"W, there is a bus decorated with pictures of sea creatures parked next to the gas station. In particular, there is a species of fish depicted twice on the side of the bus facing Haleiwa Road, each under the back two windows. What is the scientific name of this fish?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "56376d48-f456-4c24-a917-834be04c7608", "Question": "The attached spreadsheet contains the latitude and longitude coordinates of transit stops named after letters of the Greek alphabet. Which two adjacent stops have the shortest distance between them? Answer using the format Stop1, Stop2.", "Level": 2, "file_name": "56376d48-f456-4c24-a917-834be04c7608.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2baa3989-5618-4f3b-9449-8b18e3e59281", "Question": "As of August 2023, how many in-text citations on the West African Vodun Wikipedia page reference a source that was cited using Scopus?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "44eb0217-9ba3-47c8-9e1c-6325d26e6f90", "Question": "Comparing the products in the \"best overall\" ranking of a 2021 Jessica Furniss \"best store-bought\" review article and a 2023 Hannah Berman \"best store-bought\" ranking review article, there is one instance where the products in the two articles occupying the same ranking position are produced by the same company. What is this company?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f4b55162-e06c-4ac0-be0e-78546bc87204", "Question": "On October 1, 2012, Phys.org published an article about a skydiver, who also was briefly a pro boxer. Find the name of the skydiver's sole official boxing opponent.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "03c577c9-4227-48a9-9b75-f8f598de14c1", "Question": "Hi, I'm working on a project and I could use your help. I'm trying to collect a list of every street address mentioned in every science fiction story ever written. I was getting through some short stories when I came across one that I'm confident contains a street address, but unfortunately, as the story is an audiobook format and I'm hard of hearing, I wasn't quite able to understand what the narrator said. I've attached the audiobook as an mp3 file called Story.mp3. Could you please help me by telling me the address mentioned in the story? I just want the street number and street name, not the city or state names.", "Level": 2, "file_name": "03c577c9-4227-48a9-9b75-f8f598de14c1.mp3", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "198f2b40-958b-497b-a685-15fdc0587481", "Question": "In scikit-learn 1.2.2, how many different losses were supported by SGDClassifier?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "543eadae-e80a-44ba-b98d-6e8c4040dae8", "Question": "The subject of John Warham's PhD thesis was a particular genus. As of 2022, one species in this genus is extinct. Of the reference authors cited on the last 2022 version of the Wikipedia page for this extinct species, which one has initials that are a palindrome? Answer using the format First Name Initial. Last Name", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "97f452b6-f144-4224-bfe4-b6a8e0bbce6a", "Question": "One of the songs on the VNV Nation album Futureperfect has a non-English title. The title references another piece of music. Who composed it? Answer using the format First name Last name.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fe8f4748-5d00-4a27-9070-090a0cfdeac4", "Question": "The attached Excel file contains the sales of menu items for a regional fast food chain. Please tell me, in USD, how much was sold from items containing meat at the Marztep location. Round to the nearest integer.", "Level": 2, "file_name": "fe8f4748-5d00-4a27-9070-090a0cfdeac4.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e8b226bf-2ccd-4748-973d-08dad7aa8253", "Question": "What is the name of the city where the artist for the Magic: The Gathering card named \"Bedevil\" was born?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fa06024a-119e-47e9-bf90-f543a3b330b6", "Question": "As of January 1, 2021, which surviving President or Vice President of the United States was born in the town or city with the smallest population at the time of their birth? When comparing populations, please use the decennial population data available on the town or city's Wikipedia page nearest to the birth date of the individual in order to keep the population estimates consistent across comparisons. Answer using the format First Name Last Name.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d6059b3e-e1da-43b4-ac26-ecad2984909b", "Question": "Of the bricks in this set inventory that appear in 5 sets total, what is the name of the one that appears most by count in all LEGO sets on Brickset?", "Level": 3, "file_name": "d6059b3e-e1da-43b4-ac26-ecad2984909b.csv", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "208b8625-c49d-4cf1-95b6-4ced3bff82fb", "Question": "In the Annales UMCS Philology academic journal's 37th volume, what was the first guild name mentioned in a quote in its article about a video game?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "943255a6-8c56-4cf8-9faf-c74743960097", "Question": "Which of the 1994 IUPAC names for chemicals in Norman Greenwood's 1997 Pure and Applied Chemistry article that are no longer in use for any element as of the end of 2022 has the atomic number that appears first in the given CSV?", "Level": 3, "file_name": "943255a6-8c56-4cf8-9faf-c74743960097.csv", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d82180c3-39f0-4008-a883-24d5226fc49b", "Question": "What was the day in 2023 when the official NASA twitter account posted a photo with two sleeping astronauts? Answer using the format MM:DD (with leading zeros if any).", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "074f5cd6-d287-49d6-bc0a-f7d40fbf2f08", "Question": "According to a paper written by Carlos Hernandez-Suarez and Efren Murillo-Zamora and submitted to arxiv.org in 2022, how many more males are expected to die in China than females as a result of China ending its \"zero-covid\" policy? Give your answer with precision to the ones place.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2f14e114-55de-464f-af9b-82443c6ad5ba", "Question": "In the US Department of Justice antitrust division's publications, what other act is mentioned in the footnotes about the Foreign Trade Antitrust Improvements Act in the April 23, 2019 statement of interest? Just give the last name from the act's name.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "44cb62e5-b0c5-4f12-b96f-21e80ed2062d", "Question": "Find all APOD pages during the years 2011 and 2012 that have the word \"comet\" in the title, determine which comets are in these pages, then find which comet approaches the closest to the sun and which is the farthest away at perihelion using data from August 2023. Ignore any unnamed comets. Find the difference between these two distances in AU, rounding to two decimal places", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e196e50c-09fb-4a3a-81a9-c81ef41b0708", "Question": "Suppose a dog can split into two dogs to make two slightly smaller dogs 75% the weight of the original dog.\n\nI have a 180-pound dog and I define a teacup dog as a dog under 4 pounds. The dog splits itself. Then, the resulting dogs split themselves. The splitting repeats until all the resulting dogs are teacup dogs. How many teacup dogs will there be?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4d8fc0e1-69c0-4fa9-9db1-05935ec5633e", "Question": "An American actor who started his career in the 1950s shares a birthday with the Russian poet Boris Pasternak and a last name with a famous 19th Century German composer. In what city was his father born? Just provide the city name.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "bb505145-7a10-4d23-8af9-39d2b833be3f", "Question": "According to https://www.bls.gov/cps/, what was the difference in unemployment (in %) in the US in june 2009 between 20 years and over men and 20 years and over women? ", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "028f0446-9529-4be4-af42-4a25d4ccda5a", "Question": "Could you please tell me the name of the song that starts playing while Panga was playing the 2,000th level in this video, and also who wrote the song? https://www.youtube.com/watch?v=zNM7OtnJFvU\n\nI don't mean the course song, but after the first jump onto the smiling cloud blocks some different music started playing. Answer using the format SONG NAME, ARTIST NAME. Don't use special characters in the artist's name.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e463d411-6260-4d93-b5b4-6d9b1f619d5c", "Question": "In this YouTube video, how many times is the Aspirant's Bindings ability used? https://www.youtube.com/watch?v=tCaxFbNw8iM", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "1c4c108b-fd1a-4251-8a23-20efeaefcae9", "Question": "In Mambelli's \"Translating English Fictional Names\", what was the Fatica translation in Table 1 for the only noncomposite transparent surname that was a calque in both Alliata-Principe and Fatica in Table 2?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "df30cd5b-cce6-475d-907d-69927f32d006", "Question": "On the Statista chart of U.S. retail egg prices in 1995-2022, what was the price to two decimals in the highest two years that had the same price?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4810c253-7b06-447d-8bf6-64558ac5f00f", "Question": "According to BabyNames.com, what was the average rank in 2012 of the girls' first names of the first two people written in the bequeathments of the will in Taylor Swift's \"Anti-Hero\" music video?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "60fbc5a3-2805-4ad4-8eef-b58843b5053b", "Question": "According to the International Journal of Food Properties volume 22(1), which tested cooking method takes the shorter amount of time to effectively remove the substance that the authors used to test T-cell immune response in Current Zoology's volume 63(5) article on dwarf hamsters?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "80e1a640-960b-4b65-9bbf-fd94c2ec55fa", "Question": "Of the county seats in the state of Idaho, how many have a higher proportion of residents who are recorded as American Indian and Alaska Native alone than Boise? Please use the official 2020 census data to answer the question.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2372640f-a119-4960-b624-5ffbe81d6f3f", "Question": "How many decades between 1800 and 2000 (inclusive) was the US population over 50 million according to the US Census Bureau website?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9845712b-bb09-45f8-a0a4-df29b7093877", "Question": "What animal is associated with the first linked onomatopoeia on the wikipedia page about them from 27 October 2020? Answer in the singular form without articles.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8aa22ddc-fe80-464c-9c96-8d534fa5c7d5", "Question": "How many colors are used to make the face of the author of the August 2022 Discord changelog, counting different shades as different colors?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ffad7d0a-e20d-461f-b5be-07804917235d", "Question": "Give the absolute difference in numbers of applause between the two Barack Obama inaugural adress (use the official transcripts).", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "04893fc3-34fc-4117-8457-a717ad01a6a9", "Question": "The eleventh word of the seventh chapter of E. E. Smith's 1948 science fiction novel is the title of another well-known science fiction novel. This second well-known science fiction novel features an antagonistic character named after a family of birds. Please identify the two species of this family which breed in North America. Provide your answer as binomial names, in a comma separated list sorted in alphabetical order.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b7767ed5-20c7-4243-86b1-e8bd9a3d2a64", "Question": "What shape was stamped all over this restaurant's sign in March 2014, according to Google Streetview? Answer without articles.", "Level": 3, "file_name": "b7767ed5-20c7-4243-86b1-e8bd9a3d2a64.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "25575ca4-9632-428a-8789-5a6910475451", "Question": "In volume 1 of the 2021 Wyoming Community Development Authority profile, if the % employment in construction increases from 2019 to 2020 by the same percentage it did from 2017 to 2018, how many millions of dollars to the nearest million will the entire Wyoming construction industry be paying as earnings if everyone earns the 2019 median amount? Just give the number of millions.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8770c47d-56ad-42cf-b0a6-7e48d0d19019", "Question": "According to https://www.fao.org/3/ca8753en/ca8753en.pdf, how many more millions of hectares of forest in Brazil than in Canada in 2020?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0a8063da-eae2-4f84-8603-e71e8714ec23", "Question": "In the diagram of the Brazilian Army command structure created December 7th 2008 hosted by Wikimedia, how many positions are represented by a white box?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b923ed47-f382-4c56-90fa-fbafb0ea1cca", "Question": "According to the Wayback Machine, how much in dollars did the price of Charred Octopus on the dinner menu of Fabio Viviani's Riviera change from 4 February 2021 to 7 June 2023, assuming the menu is in dollars? Just give the number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "24db55fc-5bd5-402e-8b85-f01d4d50d8c2", "Question": "In the Texas A&M Science Fiction and Fantasy Research Database, how many more records are tagged with the subject \"Dragons\" than \"Unicorns\" that are from before 2020?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "21aba0e0-03d6-4541-b467-a0384ca5298a", "Question": "What color was Rihanna's shirt in the photo she posted on her Twitter account of herself with Santa Claus in December 2021?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5bbf523f-b902-4d7d-8e8d-212d00018733", "Question": "How long is the attached audio file? Limit your answer to an integer value, rounded to the nearest minute.", "Level": 1, "file_name": "5bbf523f-b902-4d7d-8e8d-212d00018733.mp3", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5093d134-9bc8-41c0-8dd2-094a59ddc002", "Question": "In the paper titled Developing Marketing Strategy Based on Engineering Menu At Ageng Restaurant, which month (full name) in which year (full number) had the highest sales amount difference between items in the category that had the highest popularity index?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "be353748-74eb-4904-8f17-f180ce087f1a", "Question": "What is the name of the person who left the only comment on the Flickr page referenced in the caption of Figure 1.1 in the attached PDF?", "Level": 2, "file_name": "be353748-74eb-4904-8f17-f180ce087f1a.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "021f63bc-b8cf-484d-a373-ca468fa8ab37", "Question": "If the current eccentricity of the comet featured in the Astronomy Picture of the Day for March 24, 2023 was subtracted from the eccentricity of the first detected interstellar object to pass through the Solar System to get the eccentricity of a hypothetical new object, how much would the new object's eccentricity fall short of a parabolic trajectory? Round your answer to two decimal places.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "70510d87-5004-4e4a-b078-21abf699dc12", "Question": "In the provided file, on what page number does a character shriek?", "Level": 1, "file_name": "70510d87-5004-4e4a-b078-21abf699dc12.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "405c2f42-5d01-46d1-97eb-2e96b7cee542", "Question": "Arthur C. Clarke famously wrote a novel that was developed concurrently with the Stanley Kubrick-directed film of the same name.  How many edits were made to the Wikipedia page for that novel's sequel during the year in its title?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0a3d99c6-d130-49a8-a8bc-6cd5999cc3b4", "Question": "As of December 31, 2021, several countries had only a single female chess grandmaster. Of these countries, what was the name of the player who had the lowest peak rating at that time? Just give the family name.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2e6c39b3-5988-4569-9401-3dde97425be6", "Question": "According to Macrotrends, what was the annual percent change in 1980 population of the home city of the third institution listed above the abstract on the HAL open archive page for the article '\"What makes my queries slow?\": Subgroup Discovery for SQL Workload Analysis'?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "de41fdda-d03c-4655-a3f2-33d9a22e789a", "Question": "Rumor has it that the ingredients in Heinz ketchup increase in ascending order from the end of the list to the beginning in a series of percentages that correspond to a subset of sequential Fibonacci numbers. The subset of numbers adds up as close to 100% as any sequential subset of Fibonacci numbers can without exceeding it, and water contributes the remaining percentage. Based on this rumor, to fill a 907g bottle with Heinz ketchup according to the ingredients used in 2021, how many more milliliters of vinegar than water to the nearest milliliter would be needed using a vinegar density of 0.972 g/mL and the rounded integer value of water density?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7c215d46-91c7-424e-9f22-37d43ab73ea6", "Question": "The attached file is the broadcast schedule for ZBC Television Networks. They have two channels: ZBC 00, which is geared toward families, and ZBC 03, which targets a more mature audience. According to this schedule, for how many hours is Time Parking aired?", "Level": 1, "file_name": "7c215d46-91c7-424e-9f22-37d43ab73ea6.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "00f49e33-3f00-4e8d-9ea5-a85bc7f3462f", "Question": "The article \u201cTechnology in the Dystopian Novel\u201d by Gorman Beauchamp begins with a block quote attributed to a novelist from the Victorian era. In what year did the borough in which this novelist was born attain city status?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "55caccf7-8c3b-4d40-842a-825789f55dd7", "Question": "On the theatrical release poster shown on the Wikipedia page for the movie \"The Hangover\" as of August 2023, one of the characters is missing a tooth. What is the number of this tooth in the Universal Numbering System?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4d317450-948c-4445-b15a-a170813a05e5", "Question": "You know that lady who played Peggy Carter in the Marvel movies and that show Agent Carter? I'm pretty sure she was also in a TV show with the guy who was in one of the Harry Potter movies. It was the guy who played Bill Weasley. What was the name of the episode of the TV show they were both in? The show I'm thinking of would have been on the air before 2020.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "427348d3-797e-4bfc-88ef-d5780f982d58", "Question": "During the US Supreme Court's October Term 2020, one of the justices wrote the principal opinion of the Court in a case where the name of the first defendant party is also the name of a world capital. For what percentage of the cases decided by the Supreme Court during the 2019, 2020, and 2021 terms combined did this justice agree with the Court's judgment? Give the answer as a percentage rounded to the nearest tenth of a percent.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "67592f4a-f0ce-4d1f-be02-fde74748926f", "Question": "Back when Coppedge first filed suit against Jet Propulsion Laboratory, the Glendale News-Press published a story about it on March 20th. According to the version of this story available on the LA Times' website on June 8th, 2023, what was the twitter handle of the reporter who wrote the piece? Include the @ sign before it.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6dc2ac8a-dfb8-48bc-a9f0-73d5435106b1", "Question": "According to the Library of Congress website archived on The Wayback Machine on May 16, 2023, one of the scrolling banners at the top of the homepage featuring artwork of a lady in \u201cRoaring 20s\u201d attire singing into a microphone advertises a festival being hosted by a theatre. What is the cost of the festival pass? Just give the number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "86ca62df-b518-48e7-9115-1b0b800e5453", "Question": "I attached a CSV file showing precipitation amounts for the five boroughs of New York last year. Can you tell me how much rain Manhattan received in the summer of that year? You can include the precipitation from June, July, and August as counting toward that summer number.", "Level": 1, "file_name": "86ca62df-b518-48e7-9115-1b0b800e5453.csv", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7805912b-c8da-4134-9b54-b590f884352a", "Question": "The .csv file I attached has precipitation amounts, in inches, for the five boroughs of New York City in a certain year. Which borough got more precipitation that year?", "Level": 1, "file_name": "7805912b-c8da-4134-9b54-b590f884352a.csv", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5c418120-2a9f-4bae-8aee-d37ac26f8465", "Question": "6001 Lancaster Ave in Philadelphia, Pennsylvania, used to be home to a restaurant called \u201cZeke\u2019s Mainline BBQ.\u201d  According to Google Street View, and prior to 2023, what color was the word \u201cBBQ\u201d on the building\u2019s sign? Express your answer in all caps.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d9060545-9e29-4a0b-8a1d-57211c462637", "Question": "A group of children gathered to play a game. The game involved rolling dice several times and totaling the score. Each player took two dice from a bag in a blind draw. Each die in the bag had six sides, but the sides had different values. There were four players, Kevin, Jessica, James, and Sandy, and one referee, Bruce. The bag held the following ten dice, with the following values on each face.\n\nThe blue die: 2,4,6,8,10,12\nThe red die: 2,2,4,4,6,6\nThe green die: 5,5,5,5,5,5\nThe white die: 6,6,6,6,6,6\nThe black die: 0,0,0,0,0,0\nThe orange die: 2,4,2,4,2,4\nThe yellow die: 2,4,2,4,2,4\nThe rainbow die: 10,10,10,0,0,0\nThe clear die: 2,2,4,4,6,6\nThe brown die: 4,0,4,0,4,0\n\nThe rules of the game were simple. Each of the players would draw one die from the bag in sequence. After all of the players had drawn two dice from the bag, they would be given a sheet of paper and a pencil. The players would then each go and roll their pair of dice 10 times, and write down the total for each roll. They would then add up all 10 of the totals, and this was their final score.\n\nAfter all of the players were done rolling dice, they returned to the referee and submitted their final score. The referee then had to decide who the winner of the game was.\n\nThe winner of the game was the player who reported the highest score. However, if a player made a mistake adding up their score or if the player cheated and wrote down a score that they couldn't have earned by playing, the player was disqualified.\n\nKevin reported his score was 185\nJessica reported her score was 42\nJames reported his score was 17\nSandy reported her score was 77\n\nThe only information Bruce had as the referee was the scores reported by the players, and the knowledge that the two dice that hadn't been drawn were the black die and the rainbow die.\n\nWho did Bruce declare as the winner?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2698c8fa-868a-4ce6-9290-6cb397d1e071", "Question": "Among the book reviews published in the science journal Nature in 2022, one was for a book about a Nobel Prize-winning microbiologist and another was for a book about the physicist behind the God particle. How many issues apart were the issues of Nature that these two book reviews appeared in?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fcd80879-4f1d-49d8-b6d6-2993607432c2", "Question": "The attached Excel file contains a record of the animals living at the San Cecelia Zoo. Specifically, it lists the type of animal, the number the zoo has, its location in the zoo, additional remarks from the zookeeper, and the date that type of animal was added to the zoo. What percentage of the zoo\u2019s meerkats were above ground when the file was created? Use a decimal format, like 0.1 for 10%.", "Level": 1, "file_name": "fcd80879-4f1d-49d8-b6d6-2993607432c2.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e8df5ee8-e804-4ab4-a5b5-f737ffb7c37a", "Question": "Two moles of a chemical compound composed of elements that spells out \"OUCH2\" is suspended in a solution nine moles of hydrochloric acid In a beaker that weighs 130 grams.  It is heated so that one mole of chlorine gas evolves from the solution and is vacuumed away by a hood vent, then allowed to cool to standard temperature. What would the weight of the beaker and remaining chemicals be together in grams, assuming standard pressure and rounding all the elements' weights down to the nearest whole number before calculating? I want the number in grams, but give me only the number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "23bcfab0-f47b-4dcb-8599-459c329ac153", "Question": "Go to https://www.youtube.com/watch?v=YR1L56mYLF4 and take note of what city the game will be set. Go to the Wikipedia page for that location. Listen to the audio file provided and navigate to the revision date indicated in that file. Navigate to the Geography section and find the only picture that is embedded there. What type of geographical feature is the focus of this picture? Give the name without any article.", "Level": 2, "file_name": "23bcfab0-f47b-4dcb-8599-459c329ac153.mp3", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "82b89810-1217-4ad8-aa9f-26e7c74ba6e5", "Question": "Between its creation and the end of 2019 (included), how many more website links than media were added to the Encyclopedia Britannica article about Walmart?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "68d05bcc-e6cd-43f1-8e9e-e73d507cd02f", "Question": "Part of the Fc\u7eacRs R-mediated phagocytosis pathway in the organismal immune system involves agglutination. What compound mediates this process, giving the name as it appears in the title of its Wikipedia article?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b1a37359-2e2e-4521-bb3d-cecdf3ec5419", "Question": "According to data from Yahoo Finance, on the day in 2020 that the AUD/USD exchange rate reached its lowest value, what was the difference between the day's high value and low value for the AUD/USD exchange rate as a percentage of the open value? Give your answer as a percentage rounded to the nearest tenth of a percent.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "1276e220-1482-424f-9c05-ae459f9de38e", "Question": "On Google Street View, in November 2022, a white van with the Union Jack on the hood was parked one lane and two streets west of the London home of Sir Arthur Conan Doyle's famous detective. According to the website listed on the van, archived on The Wayback Machine on March 21, 2023, for how many years are basement waterproofing jobs guaranteed?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7e7d97af-7472-4150-815d-8a6400aa0dc8", "Question": "What is the name of the person standing on the front of the first picture in the latest 2022 Wikipedia article on alt-j? Answer using the format First name Last name", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ed7354ce-7c07-47b0-bc2a-d9f5bbc50bc7", "Question": "A 1971 film starring George C. Scott and Joanne Woodward lends its name to a popular music group.  On that band's third studio album there is a song named for a city.  What is the difference in population between that city's most populous and least populous districts, as they were listed on Wikipedia in February of 2023?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "91d929df-7666-4eed-894c-1bafe5b2e883", "Question": "Who was quoted in the image on the Neilsen website case study about Tiktok from between 2020 and 2022?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "15b85766-24c0-4ee9-98f7-3ff237c89dc8", "Question": "In the June 2018 issue of Hochschule Anhalt Architektur on opendata.uni-halle.de, what fruit was mentioned in the title of a locked article? Answer in English, singular form, without article.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e87720bf-7dbe-49c2-b91c-bfc23b14d157", "Question": "When HuggingFace twitter account announced an open source AI event in SF in 2023 with expanded capacity, what was the new expanded capacity?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "171dd6d2-d1d4-439b-8d4e-7507018a816b", "Question": "In the provided screenshot, which problem after solving gives the highest value of x? Just give the problem number.", "Level": 1, "file_name": "171dd6d2-d1d4-439b-8d4e-7507018a816b.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e013fc18-abfc-4101-9acc-6273112517f6", "Question": "Given $f(x) = x^3 - 5x^2 + 2x, x_0 = 1$ what is the smallest $n$ where Newton's Method results in a root approximation that has a 25 digit denominator when the fraction is fully reduced?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "456cd699-e6f0-4729-a18e-077ffc7337c0", "Question": "As of 2020, two Test cricket teams were tied for the most players with 10,000 or more runs, and two Test cricket teams were tied with only one player with 10,000 or more runs.\n\nOut of these four teams, what is the height in centimeters of the tallest player who has scored at least 10,000 runs in Tests? Limit your response to the numerical value, without any units.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "680d7d77-c0c7-49c8-88fd-f8ec623645e9", "Question": "The attached file shows a list of titles owned by Scribe County Public Library. The library charges a flat rate of $3 for each item that is overdue. Assuming that everybody with an overdue library book will eventually return it and pay the fee, how many dollars of revenue can the library expect to see from overdue Adult and Young Adult books, and not including books marked as Science Fiction? Please provide your response as an integer value with no dollar sign.", "Level": 1, "file_name": "680d7d77-c0c7-49c8-88fd-f8ec623645e9.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "220a2b08-ffdc-4665-af4e-025670f5408b", "Question": "According to english wikipedia, how many studio albums did Linkin Park release until 2022? ", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "dbb02ff7-f947-491b-9ce2-41e3df16dbb8", "Question": "I'm calculating data about a population with measurements in the millions. What is the absolute difference between the output of the Python code in the file and the output if the first population grows by 2 million to the nearest 5 decimal places?", "Level": 2, "file_name": "dbb02ff7-f947-491b-9ce2-41e3df16dbb8.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e58ea654-994a-44fd-aaff-bf033c715c56", "Question": "What is the name of the sugar component of the chemical in Scheme 2 of the JACS Au article about Azide-Masked Fluorescents from Volume 3 issue 4 that has the highest shown percentage?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3c5f0280-b1a3-43cf-817e-c3fa0016b1e2", "Question": "In the first RSAI journal issue from the month that oganesson was officially named, what was the email given to contact the female author of the second article?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e47bd787-8024-425e-985c-7809bd992fa5", "Question": "As of the last post in September 2016 on candyblog.net, what was the average number of posts in the \"Type\" categories starting with M rounded to the nearest whole number?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fb59de38-e688-43f9-a959-a687e6cd0ebc", "Question": "If you had a gallon of mayonnaise, how many Chocolate Mayonnaise Cakes could you bake using Southern Living's recipe, last updated November 2022?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "355b827f-fff0-4e0c-9ff0-65dea0609838", "Question": "The diagram in the attached Excel file represents a game of Tic-Tac-Toe, where X\u2019s are represented by red spaces, and O\u2019s are represented by blue spaces. Where should O place its next letter to win the game? Express your answer as a set of x-y coordinates, formatted as x, y. 0, 0 is the bottom left corner of the Tic-Tac-Toe board.", "Level": 1, "file_name": "355b827f-fff0-4e0c-9ff0-65dea0609838.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "a15f8d90-115d-4376-98b3-c866be21ef8f", "Question": "As of 2022, in the city with a commercial airport people can fly to, a pine tree that only grows in two locations, and a zoo, what endangered alligator species can be found in the part of the zoo With alligators? Answer using singular form.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "adbc62c6-dc67-4bd8-a245-e1ed36b0d733", "Question": "In what directory could the public find the free Penguin Project dating software for Mac as of 1997?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ca94a79c-30ee-4c9c-9be6-57b4bb4284d3", "Question": "One of director Judd Apatow's films features characters using video game controllers from one console to control a game made for a separate console. What is the difference in speed of the main processors in these consoles? Please report your answer as a numerical value followed by either MHz or GHz.\n\nIt is possible that multiple revisions of a video game console may have different processors. If this is the case, select the earliest revision of both consoles for the purposes of your computation. Also, it is possible that the video game displayed in the scene was made available for multiple consoles. If this is the case, report the difference in processor speed for each possible combination, in a comma separated list without units, ascending from smallest difference to largest difference.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e26e4f19-0c1c-430d-a881-66e52b9043f5", "Question": "The Histomap of world history created in 1931 states that a particular Turkish ruler failed to arrest the decline of his empire. What was the birth name of this ruler's chief consort?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "aac4df0d-407a-45f2-add5-d9b31ebe1ddc", "Question": "The provided spreadsheet lists locomotives owned by a certain railroad museum. Some of these locomotives run excursions to destinations in the region. For example, the Main Street Jaunt runs 10 miles to the town\u2019s historic main street, the Sunset Picnic Trip runs to a scenic picnic grove 50 miles away, and the Fall Foliage Tour traverses a 60-mile route around a mountain. Locomotive #93 is about to leave for its assigned excursion. There is a hot air balloon show today that is visible from its destination. The show, however, ends in an hour and a half. If this locomotive travels at its top speed for the entire duration of the journey, will it arrive at its destination before the end of the show? Yes or No?", "Level": 2, "file_name": "aac4df0d-407a-45f2-add5-d9b31ebe1ddc.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "698799a7-33df-4892-9ecd-76600ad91b96", "Question": "Which animals of the Chinese zodiac are present in the painting in the collection of the Philadelphia Museum of Art with the accession number 1950-92-7? Give the answer as a comma-separated list ordered by how close they are to the front of the queue in the painting, starting from closest to the front. Use the singular forms of the animal names as listed on the Wikipedia page for Chinese zodiac as of August 2023.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "66bd1b1c-443b-4b4e-a108-0fa06527dd62", "Question": "If you translate the scientific name of the Black rhinoceros and then sum the number of horns referenced in total, how many horns do you get?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "302a5b22-7599-4f9d-966b-445db74a0533", "Question": "What is the text between the opening curly brace and the first comma of the BIBTEX of the PhilPapers.org article on African philosophy aesthetics from 2006 by Jane Duran?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8b553092-3d44-4ab3-8d1e-932aabc1e143", "Question": "In the chess game in the attached image, White can mate in 1 by playing a particular type of move. The name of this type of move is also the name of an electronic music album whose cover shows a robot arm playing chess using a physical chessboard. In the album cover image, on which square is there a black piece that is en prise, ignoring the piece that the robot arm is holding? Assume that the white king is on its starting square in the album cover image. Give the answer in algebraic notation.", "Level": 3, "file_name": "8b553092-3d44-4ab3-8d1e-932aabc1e143.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c16acedc-7a4e-4df5-a007-200faa5c67e5", "Question": "On the 18th page of the official instruction manual for the Lego set with the ID number 4852, four instances of a certain light gray piece are added to the build. On what page of the manual for set 4855 is that same piece first added to that set\u2019s build?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "1b738608-485d-4456-98d3-31ad60f77284", "Question": "In the video of the 2022 finals on the Poetry By Heart homepage, how many judges are shown speaking?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9961d869-f827-4540-bcf0-1e09c2cba3b0", "Question": "From the chess.com homepage on the date of 8/30/99, list each green chess piece that has text under it in the horizontal toolbar at the top, in order of appearance from left to right. Use only lowercase letters to describe the pieces, and provide your answer as a comma separated list.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ff43a8cd-3850-42a4-bdb1-1eea53a8fcb7", "Question": "How many pull requests were merged in the numpy github repository in 2021?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d8f6be13-17f3-450a-b68f-89d7de943cf1", "Question": "To the nearest 15 minutes, how far apart in the day in HR:MN were the photos from the earliest 2023 Wikipedia page for the Big Ben or the Rajabai Clock Tower taken? Answer without leading zero if any and don't include AM or PM.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ed5e2ddd-6a3b-43a2-b6dc-a6c8862e06d7", "Question": "In the painting in the Museum of Modern Art's collection with object number 235.1991, the first letters of each row of text in the painting spell out the name of a village located in which district of Azerbaijan? Give the name of the district by itself without \"District\" or any other administrative division after it.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "beeee382-3b29-4be2-b6c7-e54e3543c93f", "Question": "How many descendants of Keturah are named in 1 Chronicles 1:32? If there is a discrepancy between different translations, use the New International Version.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7245af7c-404e-4d60-9ef4-94ed301e5315", "Question": "The paint sample in the upper center of the attached image has a punny name. What word is the sample\u2019s name meant to sound like?", "Level": 1, "file_name": "7245af7c-404e-4d60-9ef4-94ed301e5315.jpg", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8488761b-6d5a-4be0-86d6-5cf8d6c13efe", "Question": "On DrugBank, what is the brand name of the topical use antivaricose cardiovascular vasoprotective that appears twice at that level in the ATC classification tree?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "1dd3f5f0-a467-4336-b1e1-78ea886a2244", "Question": "As of the end of July 2023, how many other rail lines did Amtrak\u2019s Adirondack line provide connections to? Count only commuter/heavy rail lines that share a station with the Adirondack, not subway or light rail lines.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cadc593c-ebde-43ec-afa6-a8a4e95c0e38", "Question": "Find the essay that begins on page 32 of Volume 8, Issue 8 of the Russell Sage Foundation Journal of the Social Sciences. What two-word term do the authors use beginning on page 34 that describes how peoples\u2019 actions are influenced by the actions of people they come into contact with?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cbdb17dc-62d9-4463-b648-2eaacfeba4e5", "Question": "What number is drawn by the coloring of certain cells in the attached Excel file?", "Level": 1, "file_name": "cbdb17dc-62d9-4463-b648-2eaacfeba4e5.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b78952b8-af19-4ffa-9275-cfa41a255b8a", "Question": "Sum the coefficients of the cubic term of the indefinite integrals of the following equations:\n\n95x^3-3x^2-19x-1\n13x^2-5^x-38\n-19x^5+x^4+2x^3-3x^2-5^x+10\nx^3+5x^2\n\u2212102x^5-x^4-12x^2-4x", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6178671d-6f80-4e0d-9672-54afaf7b527b", "Question": "In the paper by Yana Manova-Georgieva in May 2020 about character names, what character is named in both sections 2.2 and 2.3?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3415b0bd-f53f-4397-b657-b3204d43235e", "Question": "Navigate to 486 Seven Seas Drive, Orlando, Florida in Google Street View. In the 2008 view of this address, find the white box truck. Then, navigate in Google Street View to the address given on the side of that truck. In the 2022 view of the address on the truck, there is a sign on a pole. What three words are written in blue letters on this sign?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "07c3029f-7095-455d-a9e9-cd5e34001b38", "Question": "According to this JSON, who discovered the first metal listed with a null molar heat?", "Level": 1, "file_name": "07c3029f-7095-455d-a9e9-cd5e34001b38.json", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b3654e47-4307-442c-a09c-945b33b913c6", "Question": "The attached file shows a list of titles owned by the Scribe County Public Library. Find the age range that has the highest percentage of books authored, at least in part, by women. Then, tell me what percentage of books in that age range are fantasy books. Round to a whole number for your answer, and don't include a percent sign.", "Level": 2, "file_name": "b3654e47-4307-442c-a09c-945b33b913c6.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "01af5e27-e389-4c69-a133-412e16532b67", "Question": "What is the atomic number of the element mentioned in the first line of Nikki Giovanni\u2019s poem \u201cThe Laws of Motion\u201d?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d543b75f-96e6-4b04-bf2c-60acd0d48246", "Question": "In numpy 1.24, how many modes does the qr factorization function support vs numpy 1.13 ? Answer with the absolute difference.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e5de797e-4429-425e-80b4-2fed4620c52b", "Question": "In the 2018 Conseil National press release for the industry that the original company behind Cabbage Patch Kids began in, what percent of the French GDP for the same year as reported by the World Bank was the turnover mentioned in the press release in Euro? Use the average 2018 exchange rate from USD given by Exchange Rates UK for that year and rounded to the nearest thousandth of a percent.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "70e18502-7ff4-4aa7-85ed-f5587cc582c9", "Question": "In the article titled \u201cThe Presentation of Self in Virtual Life\u201d in the December 2022 edition of The Russell Sage Foundation Journal of the Social Sciences, how many more instances of COVID-19 misinformation links identified by healthfeedback.org did the authors find on Twitter than on Facebook?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "68ccf11a-bcd3-41e5-a5ee-3e29253449e9", "Question": "Follow the instructions in the supplied document using the line \"Twinkle twinkle little star, how I wonder what you are\" as the key. What is the word?", "Level": 1, "file_name": "68ccf11a-bcd3-41e5-a5ee-3e29253449e9.docx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f1ba834a-3bcb-4e55-836c-06cc1e2ccb9f", "Question": "I lost my place in this book file; the culprit had just been introduced. What line number was I on?", "Level": 1, "file_name": "f1ba834a-3bcb-4e55-836c-06cc1e2ccb9f.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "dd024dd9-8da6-4d4e-aee1-ed0d999035a9", "Question": "You are the manager of a shop that opens at 8 am and closes at 8 pm. The reference file contains a list of workers and their shifts. Each time slot indicates when the worker begins their shift at the shop and ends their shift at the shop. What is the total amount of time when exactly two workers are at your shop? Your answer should be a numerical value and in minutes.", "Level": 1, "file_name": "dd024dd9-8da6-4d4e-aee1-ed0d999035a9.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "634fca59-03b2-4cdf-9ce4-0205df22f256", "Question": "Under PLOS One authorship guidelines, what contributor roles should the individual in the file be given when the article about the study in question is submitted? Separate your answers with commas and use the alphabetical order.", "Level": 3, "file_name": "634fca59-03b2-4cdf-9ce4-0205df22f256.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b4d2d683-7870-4940-8eaf-03cd1355873a", "Question": "On BASE Search, find the UK CC0 video for poor youths. To what seaside town were they proposing to send them?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "98efafc6-c376-4b53-be91-a130e1d90e02", "Question": "Of the two LEGO sets that contain the part in the attached image, which 4-digit set number was part of an earlier-created media franchise?", "Level": 3, "file_name": "98efafc6-c376-4b53-be91-a130e1d90e02.jpg", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c7adc367-cf65-43f5-acc6-b3cc8b61c51e", "Question": "Google Street View\u2019s June 2014 imagery for the New Jersey side of the Lincoln Tunnel shows an ad for a smartphone above each of the tunnel entrances. What date was this phone released? Use the MM/DD/YYYY format.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "900bb2d0-c2ae-43a6-b25b-62f96c3770e3", "Question": "In 2009, Lego released a model of a famous building that stands at 39.9063\u00b0 N, 79.4678\u00b0 W. In the third step of the instructions for this Lego set, one gray brick is added. What was Lego\u2019s original, four-digit ID number for this brick?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "314b3c26-3e8d-4f8c-a9aa-c77275072e0b", "Question": "Pick a random integer. Now, count the letters that make up that number to give you a new number. Repeat this process until you repeat the same number twice. What is your number? Express it as a numerical character.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "41220f73-ec72-47c1-ba53-a6e50e4896b9", "Question": "In the arXiv paper about APIScanner, what is the absolute difference between the standard deviations of the LOC values in Table 1 when calculated using the default standard deviation function for the third and fourth libraries in the table? Round the answer to the most significant decimal place whose value is the lowest prime digit.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "27ee81e0-9f94-4b2d-8ab5-3c7033fcefca", "Question": "According to a paper written by Ari Allyn-Feuer and Ted Sanders that was submitted to arxiv.org on 5 June 2023, what is the exact probability that artificial general intelligence will exist by 2043? Your answer should be rounded up to the nearest integer percentage.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8f515e77-1b54-4183-bd5d-3c5f71674931", "Question": "What was the first video game in which the voice actor who said the line, \"You always sense a disturbance in the Force,\" voiced any role in the same franchise?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "967ad395-7b16-43a2-83e7-41df7cd6401a", "Question": "Biochemist Glenn Kuehn's first publication showed the percent of ^14C present as two different chemicals for fixation times of 6 and 12 seconds during ribose oxidation by Hydrogenomonas facilis. Increasing the fixation time from 6 to 12 seconds resulted in an increase in the percent of ^14C present as one of these chemicals. What was the increase in percent of ^14C present for this chemical when fixation time increased from 6 seconds to 12 seconds? Give your answer with two significant figures.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "afd1efe6-03dd-478c-9eb1-e562355ee94e", "Question": "Please take a look at the story I've attached as Test_text.txt\n\nThe file is a novel, and in its contents, various years are mentioned as four digit numbers. If you find the earliest year listed in the document, you will be able to address the following question:\n\nAs of August 1, 2023, the Wikipedia listing for the year mentioned above references a company's patent.\nAccording to Wikipedia, how many more employees worked for this company in 2020, compared to 2010?", "Level": 2, "file_name": "afd1efe6-03dd-478c-9eb1-e562355ee94e.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f30a836f-420f-4738-80af-d2491e072dab", "Question": "What is the dominant hand of the lowest visible character in the top splash art in the article entitled \"Subclasses, Part 4\" in the Dungeons and Dragons website's Unearthed Arcana section? Answer only using Left or Right.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f1746e25-adf7-4565-8365-56f3d3536c92", "Question": "In the YouTuber slowbeef's playlist \"Blind Nuzlocke Pokemon (Grimer is the Best)\", what is the sum of the Pok\u00e9dex numbers for all Pok\u00e9mon that appear in the thumbnails for videos 22-26, omitting any Pok\u00e9mon that appear more than once?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cd8166fc-8a04-43b8-956a-785c6513671a", "Question": "I work for a commuter railroad that is looking to expand service into Broward County, Florida. We want to use the Broward County bus service as a yardstick for the demand for transit in the area. We are contemplating three lines: \n\n1. From the corner of Sunrise Boulevard & A1A to Sawgrass Mills Mall.\n2. Dania Beach to Broward College Central Campus.\n3. From Galt Mile to the corner of Commercial Boulevard & 94th Ave.\n\nWe want to know how much revenue each line would bring in, if we charge $5 per trip.\n\nBasically, you should use Broward County Transit\u2019s January 2023 ridership report and find that month\u2019s ridership for the routes that are equivalent to the lines I mentioned above. Then multiply the ridership by our fare to get how much revenue that line would bring us in a month. Your final answer should be the revenue of the most popular line among those three in dollars.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8a0e4077-b38a-4ccb-bfc4-99786b1f577c", "Question": "On what page of the 1975 edition of Irma S. Rombauer's best-known cookbook is the recommended stuffing for raccoon described?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9d0f88cd-f02d-4864-bf15-93ec95fef4f8", "Question": "Give the following statistics according to USDL-22-2309 using the same list format as the statistics requested. If something is a decrease, use a - to note it negative. Express percentage between 0 and 100, without the % sign. So a 5.1 percent increase would be 5.1 and a 2.3 percent decrease would be -2.3. Express your answer as a comma separated list. So if say, I asked for:\n\na. percent change in hours worked in the fishing industry\nb. percent change in fatal injuries in the mining industry\nc. number of injuries per 100,000 workers in the agricultural industry\n\nthe answer would be formatted like this: 5.2, -1.2, 21.3\n\nassuming the document referenced gave those figures.\n\nHere is the list:\n\n percent change in suicide rate from 2020 to 2021\n the lowest age in the age demographic that accounted for about 20% of fatalities\n the fatal occupational injury rate in 2021\n the actual change (not percentage or per 100000 FTEs) of violent fatalities (injuries by other humans/animals) from 2020 to 2021\n the change (not percent change) in fishing and hunting workers' fatal injuries per 100000 FTEs from 2020 to 2021\n the percent change in deaths for driver/sales workers and truck drivers", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3cc53dbf-1ab9-4d21-a56a-fc0151c10f89", "Question": "The attached file shows the inventory of a movie and video game rental store. Assuming that the store only has one copy of each title, how many cartridges does the store currently have on its shelves?", "Level": 2, "file_name": "3cc53dbf-1ab9-4d21-a56a-fc0151c10f89.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b548db79-fabd-4bbc-81f3-43097159a3a5", "Question": "In the Sotho language, the suffix -ana or one of its variants can be attached to a noun to create a specific form of the noun. In the Dutch language, what are the possible definite articles that can be used with singular nouns that are in this specific form? Give your answer as a comma-separated list in alphabetical order.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "bdb2f8ff-83a3-4288-a019-68c7968198f6", "Question": "According to english wikipedia, and until 2023, how many US presidents were born in a state that was part of the confederacy at the time of the birth?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9b98305b-af16-489e-adbc-41b5c5a0ec2d", "Question": "In July 2016, at the NARSC conference, how many more dollars was the difference between student pre-registration and advance student registration than late student registration and advance student registration?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "22f61d58-b420-4afb-a641-39ef09c65603", "Question": "In the longest season of the Writing Excuses podcast prior to 2020, what was the off-topic question asked in the earliest microcasting episode?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d50b8ecb-a8aa-4696-ad84-403ef15e2c8b", "Question": "The attached PDF shows accommodations in the resort town of Seahorse Island. What is the average rating that accommodations in this town have received?", "Level": 2, "file_name": "d50b8ecb-a8aa-4696-ad84-403ef15e2c8b.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b01a3c5f-56ce-4fee-bc15-afd2047bc976", "Question": "Reverse this ascii picture so that the fish is facing the opposite direction:\n>>$()>. Return the characters (without quotes) in a comma separated list.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "cebff819-f4d7-4b4a-8c42-31bed1a062c5", "Question": "What is the correct way to spell the first misspelled word in the February 22, 2012 paper \"Biochemistry: Enzyme Kinetics\"?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8f2e558f-c8a3-4e00-81fd-d52d62917977", "Question": "Article number 17591 of Scientific Reports 9 includes some supplemental information. For the measured variable that has the highest cumulative standard error across all rows of data in the supplemental materials, what is the average standard error, rounded to two significant figures?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f2ea141f-073c-4a86-af4c-fd1f2e7903fc", "Question": "How many guanine nucleotides are found in the Ad_ITR primer?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "985ec22e-546b-49fc-ab3c-af490fbefdf3", "Question": "The OG BDFL is mentioned in this file at one point right before a person who wrote a book on optimization. What vital question was asked in the table of contents of volume A?", "Level": 3, "file_name": "985ec22e-546b-49fc-ab3c-af490fbefdf3.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2131de62-c9b7-4f08-a28f-ccbd9e4809f3", "Question": "In the video located at https://www.youtube.com/watch?v=X-AjhXhk19U, what character is playing the violin throughout the video?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5f2b2e54-5047-4394-81be-198230c3b508", "Question": "The attached spreadsheet shows the inventory for a movie and video game rental store. Right now, they\u2019re holding a \u201c90\u2019s Week\u201d sale, where all DVDs and video games from that decade are 30% off. How many titles qualify for the sale and are available for customers to check out?", "Level": 2, "file_name": "5f2b2e54-5047-4394-81be-198230c3b508.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f08e2efd-1885-4437-8c88-90a20558ad1d", "Question": "At the beginning of glycolysis, glucose is converted to glucose-6-phosphate. At the end of glycolysis, what atoms are connected directly to that 6th C atom? Return the elemental symbols in alphabetical order comma separated, don't deduplicate.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d219675a-0552-4fe0-8c5a-ae7365501f00", "Question": "How many footnotes are in the essay that Ming-Hsun Lin wrote in 2010, comparing Harry Potter to traditional princess stories?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9aefe06d-0921-4941-96b4-154de9ee4f05", "Question": "How many right turns does a monorail traveling from the Las Vegas Convention Center to the LINQ Hotel make? You can consider a turn to be any instance where the monorail track changes the nearby road that it runs parallel to, even if that turn is less than 90 degrees.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "bb686773-a454-44d5-b193-cfccfdb90dee", "Question": "What editor's note accompanies the edit on the Wikipedia page for Northern Territory National Emergency Response that removed the most text from the article?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "633d1eae-8748-43e3-889a-0830c1c870c4", "Question": "My two friends and I have gotten together and brought two ingredients each. I brought chicken and turkey, my friend brought spinach and arugula, and my other friend brought canned biscuit dough and canned pizza dough. If we use one ingredient from each of us, what is the title of the one Grand Prize-Winning Pillsbury Bake-Off recipe from before 2020 that we can make?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2b2e4e18-a825-4285-aeb1-8d93d2521627", "Question": "Which county in Pennsylvania that is served by Amtrak\u2019s Keystone Service route had the highest Amtrak ridership in the 2021 fiscal year? (Based on the combined ridership of that county\u2019s stations). Just give the county's name.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "59b3cfb0-a06a-4ac5-b54e-81c9db8b0957", "Question": "I\u2019m interested in learning about how the place a person lives in affects their preferred communication method. The attached dataset contains the location and contact information for a group of twelve survey respondents. What is the absolute difference between the percentage of people in Palmetto who use carrier pigeons, and the percentage of people in Coconut who use them? Round to two decimal places for your calculations, and don\u2019t put a percent sign in your answer.", "Level": 2, "file_name": "59b3cfb0-a06a-4ac5-b54e-81c9db8b0957.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "32f386b9-73ee-4455-b412-ddad508aa979", "Question": "The attached file shows the broadcast schedule for ZBC Networks on August 3rd, 2022. ZBC operates two channels: ZBC 00, which is geared toward families, and ZBC 03, which targets a more mature audience. On the date shown, how many shows are broadcasting episodes from their first season on ZBC 03? Each entry in the episode column contains a prefix, e.g. \u201cS9E13\u201d, where the number after \u201cS\u201d is the season number and the number after \u201cE\u201d is the episode number.", "Level": 1, "file_name": "32f386b9-73ee-4455-b412-ddad508aa979.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f2fa52f6-fc8a-498c-98d3-17f66c848d1b", "Question": "According to the 2011 Indian Census, how many rural women in the state of Goa who stated they were Scheduled Caste (Dalit) who gave their age as between 25 and 40 stated their educational level was a technical degree or diploma equal to degree or post graduate degree in Engineering and technology?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d62cbee6-47c7-4918-825d-3b73b1af7e85", "Question": "Please review the provided matrix of images. The final cell is currently not filled. At the bottom of the image are four possible choices, each associated with a letter, A through D. Provide the letter corresponding to the image which correctly completes this matrix.", "Level": 1, "file_name": "d62cbee6-47c7-4918-825d-3b73b1af7e85.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "91f2bf12-5280-4efc-b9a7-26e67ca850b4", "Question": "How many cats are in the attached photo, including those that are partially obscured or not fully in frame?", "Level": 1, "file_name": "91f2bf12-5280-4efc-b9a7-26e67ca850b4.jpg", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "16cf70d8-9263-4eb0-a8a9-5eb91a23b462", "Question": "For each pair of adjacent stops on Amtrak\u2019s northbound Adirondack route in the U.S. (as of July 2023), calculate the ratio between the percent change in 2021 Amtrak ridership of each stop in the pair, and the number of miles between them. Tell me the name of the station pair that this ratio is the lowest for in a comma separated list in alphabetical order, e.g. Saratoga Springs, Schenectady", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0e4aa403-de9a-4f69-be4a-b141184db4a9", "Question": "As of July 2023, the Wikipedia page for penguins links to the Wikipedia pages for specific penguin species in the body of the article. The last such link goes to a page that has a link to a 4-page PDF file that mentions emperor penguins. The second to last link to a specific penguin species goes to a page that uses a PLOS One reference, which also mentions emperor penguins. How many more times are emperor penguins mentioned in the PDF file than in the PLOS One reference?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "07c930f4-40a4-4be7-9165-ce8ad24bdbf9", "Question": "On the wetteronline.de website, how many of the photos in the March 2023 \"Bilder von bunten Wiesen\" of the Fotostrecken collections feature both bees and flowers?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "063800f6-8832-4856-972b-17b877612533", "Question": "The board in the image is for a game. The rules of the game are as follows:\n\nThe game may have up to 6 players. Players each get one game piece. The game pieces are red, orange, indigo, cyan, magenta, and gray. Players draw a random game piece out of a provided opaque sack in order to find out playing order. The player who draws the cyan piece goes first, followed by gray, then indigo, then magenta, then orange, then red.\n\nThe game pieces start on the purple START square. Players roll a 5-sided die to see how far they move each turn, following the turn order given above. Each square is one space. The number of spaces moved corresponds to the number on the die roll. To win, a player must land on the purple FINISH square, ending the game. Each yellow square that players land on requires the player to move that many spaces backwards and play the square they land on, unless it is another yellow square. Each pink square that the players land on adds the number of points on the square to their total score. Each blue square that the players land on requires the player to move their game piece that many spaces forward and not play the square they land on, unless it is another blue square. Each green square that the players land on reduces their total score by the number of points on the square.\n\nSix players join the game: Alphonse, Makena, Caleb, Philip, Jun, and Diana. Makena draws the red piece, Alphonse draws the orange piece, Jun draws the cyan piece, Diana draws the magenta piece, and Philip draws the gray piece, and Caleb draws the indigo piece. Their die rolls in order of the turns they take are as follows:\n\n1, 4, 3, 1, 3, 3, 5, 4, 5, 4, 3, 5, 1, 4, 1, 4, 2, 3, 4, 5, 1, 2, 1, 1, 2, 1, 4, 1, 1, 5, 4, 2, 3, 5, 4, 3, 4, 1, 2, 2, 3, 1, 5, 1, 1, 4, 5, 4, 3, 3, 3, 2, 1, 2, 3, 5, 5, 5, 3, 2, 1, 1, 5, 3, 3, 1, 2, 2, 4, 2, 5, 3, 2, 3, 4, 2, 2, 1, 5, 4, 3, 2, 4, 5, 5, 3, 5, 1, 3, 4, 3, 2, 3, 3, 1, 2, 4, 1, 1, 1, 2, 4, 3, 1, 1, 2, 1, 1, 4, 5, 3, 3, 1, 2, 3, 1, 5, 5, 2, 1, 4, 5, 4, 5, 2, 2\n\nAt the end of the game, they all add up their points. Makena is the winner. Since they added up their points, what number was on the last square Makena landed on before the FINISH square?", "Level": 2, "file_name": "063800f6-8832-4856-972b-17b877612533.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c9adfc66-ee9e-437a-aa54-e76ab00d9a4a", "Question": "What was the first name cited in the introduction of the bioRxiv paper on which Fabien L. Condamine and David Peris collaborated before June 2023?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "462413b8-642c-4a4d-be27-1a9406100e31", "Question": "The Beach Boys' song \u201cAmusement Parks U.S.A.\u201d mentions a park that, along with Disneyland, \u201cis worth a trip to L.A.\u201d. Who directed the episode of the Twilight Zone (1959) that was filmed in this park? Give the director\u2019s first name, middle initial with a period, and last name.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "dfa03d6c-402b-43fc-9222-5738f8bdfd0c", "Question": "How many meals in this file have both chicken and lemon, based on the titles?", "Level": 1, "file_name": "dfa03d6c-402b-43fc-9222-5738f8bdfd0c.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e51753c7-3ef3-4404-a352-11a18e5760c9", "Question": "Please take a look at the video game image I attached to this message. I'd like to know how many times the player character can be damaged by enemies before being defeated. For the purpose of this question, assume that the player uses any health-restoring items already held optimally, but that the player does not defeat any enemies and consequently does not find any additional health-restoring items.\n\nPlease report your answer as an integer number of times the player can sustain damage prior to being defeated.", "Level": 3, "file_name": "e51753c7-3ef3-4404-a352-11a18e5760c9.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7a770333-8c1b-4008-b630-9d3cb4f0c171", "Question": "I hid my surprise in this file. What was the single top 40 song from the name after my surprise?", "Level": 3, "file_name": "7a770333-8c1b-4008-b630-9d3cb4f0c171.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c771f182-c66e-4c81-b1b8-80f924e7ca9a", "Question": "In 2021, Junsheng Zeng et al. published a paper about gravity currents. The paper referenced multiple articles published in the journal Nature. What is the first name of the credited author for the most recently published Nature article referenced in Zeng's paper?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "5cb40f44-29b4-49de-a924-03e68191a994", "Question": "According to english Wikipedia, what percentage of the highest lifetime Jeopardy winnings is the highest single-episode Wheel of Fortune winnings to the nearest whole percent at the end of 2022? Disregard the celebrity version of the game shows.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "7cf5f072-4c37-4ccb-8c4e-a8f66fcdd9e1", "Question": "The Smithsonian Institution contains a photograph with the catalog ID number of 177827. What year was the species of shark depicted in the photograph first scientifically described?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "52e8ce1c-09bd-4537-8e2d-67d1648779b9", "Question": "The attached .csv file shows precipitation amounts, in inches, for the five boroughs of New York City in a certain year. How many inches of precipitation did the city receive in total for that year? Don\u2019t use commas if the number has four or more digits.", "Level": 1, "file_name": "52e8ce1c-09bd-4537-8e2d-67d1648779b9.csv", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "763a8789-8aa9-4254-aa12-779bec9c7aef", "Question": "In the book with ISBN 9780472902903, the introduction begins with a quote. What is the title of Chapter 2 of the book that this quote is sourced from? Exclude the words \u201cChapter 2:\u201d from the title in your answer.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f9f97b63-52e5-482f-beb8-c0f3cfac163e", "Question": "In the 2021 Annual Report from the meal kit company originally funded by Rocket Internet, which supervisory board member (name only) had an M in Diversity, no marketing experience, and an exec position? Just give the last name.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e4b6ca51-c70d-45f7-90dd-cbd1790df1cb", "Question": "How many commits for the Mount Fuji page in english wikipedia in March 2023?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fb488ce0-520c-4f3b-b953-b52b4130fb06", "Question": "In the Official 2006 blackbook price guide to United States paper money found in the further reading section of the May 2023 $100 bill Wikipedia article, what is the first word of page iii?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "52fcc23e-4e68-43da-9410-f8f33632006f", "Question": "I have a fun card game that we can play together! Here are the rules: I'll draw a certain number of cards and then arrange them in a certain order in a stack, face down. After I do that, I'll describe a series of steps, and your job will be to keep track of the cards and answer a question about the final ordering.\n\nOkay, from top to bottom, here's the starting layout:\n\n2 of clubs\n3 of hearts\nKing of spades\nQueen of hearts\nJack of clubs\nAce of diamonds\n\nNow, here are the steps I'm taking to jumble up the stack.\n\nI take the top three cards and place them at the bottom of the stack.\nI take the top card and place it under the second card.\nI take the top two cards and place them under the third card.\nI take the bottom card and place it on top of the stack.\nI take the top two cards and place them under the third card.\nI take the top four cards and place them on the bottom of the stack.\nI take the bottom card and place it on the top of the stack.\nI take the top two cards and place them on the bottom of the stack.\nI take the bottom card and place it on the top of the stack.\n\n\nWhat is the top card on the stack? If the card's name contains a digit, write the digit in plain text when answering, e.g. 'six of spades' instead of '6 of spades'.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b201ae2a-fdab-4e13-a687-174d557d608a", "Question": "The URL https://3d-api.si.edu/voyager/3d_package:40cf5b52-0b21-4063-95fc-aa07998eb4dd leads to a 3d model of a vintage induction coil. What number is written on the coil?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fb349e07-d6c6-4d98-9a0e-1a3567f7d9bd", "Question": "What is the motto of the flagship university whose acronym is spelled out by the letters shaped by the graphed lines of these functions in Python MatPlotLib in order? (1) \"y = 3x^2 + 2x + 2\" (2) \"y = -3x^2 + 2x + 2\" (3) \"x = 3y^2 + 2y + 2\"", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4866d2d0-e86f-4541-9801-4faa9b910a35", "Question": "In Kingston, Ontario, there was a radar speed sign on the corner of King Street West and St. Lawrence Avenue. What is the first year that Google Street View has data showing a radar speed sign on this corner?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "6de7d951-8dfe-449f-a808-76e2359b75b8", "Question": "According to Macrotrends, what was the annual percent change in Norfolk Southern\u2019s stock price for the year that it was approved to acquire Conrail? (Don\u2019t put the percent sign in your answer).", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "84dff22d-5520-49f7-9c0a-d4b7a49de8cf", "Question": "In a 2022 interview with TED, screenwriter Michael Schur describes the first season of a show he worked on, saying that every episode in the season ended on an unpleasant note. Find the building used for exterior shots of that show\u2019s main setting after season one. Railroad tracks run behind the buildings across the street from that building. According to Yahoo Finance, what was the opening stock price on February 1, 1980 of the railroad that owned those tracks as of 2022?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b74b4ce7-4f03-42b5-b60e-62da7ffa282e", "Question": "The attached spreadsheet contains a list of occupants of the Liminal Springs Mall. It also lists each occupant\u2019s revenue for this month as well as the rent they pay the mall. What is the name of the restaurant that made the most money, relative to the rent it paid?", "Level": 2, "file_name": "b74b4ce7-4f03-42b5-b60e-62da7ffa282e.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d366cc70-86f1-4dca-bf12-440479c825fe", "Question": "Two people play a card game where they each draw a card and the high card wins. Player 1 always drew red suits, and player 2 always drew black suits. Each round adds 1 point to the winner's score plus a bonus point for a face card or two bonus points for an ace. The highest score at the end of 15 rounds wins. Based on the presentation of the card game rounds, what is the absolute score difference between Player 1 and Player 2?", "Level": 2, "file_name": "d366cc70-86f1-4dca-bf12-440479c825fe.pptx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4dccff35-158b-4647-8ad0-b7432112077d", "Question": "What is the Wikispecies Subordo as of 2022 for the genus studied in the article about Food For Everyone from one of the authors of the importance of genetic tools when studying the distribution of rare and elusive species illustrated by the Kam dwarf hamster?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "3013b87b-dc19-466a-b803-6b7239b9fd9c", "Question": "From the earliest record in the FDIC's Failed Bank List to 2022, what is the difference between the highest total paid dividend percentage from a Pennsylvania bank and a Virginia bank? Just give the number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "********-08b7-4627-a076-be6f4b3a95c8", "Question": "In Goudie's 207-page duck paper compilation from 2006 found on a Memorial University website, between what hours did data logging occur every day to happen at the same time as flyovers from military aerial vehicles, in military time, numbers separated by a hyphen only?", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ed30357a-9648-49fd-8d34-a78e71d0984c", "Question": "What is the percent decrease between videos uploaded by the YouTube channel \u201cNot Just Bikes\u201d in the year 2021, and videos uploaded by that same channel in the year 2022? Round to two decimal places.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "69b557be-22a3-48fd-971c-0381d867ec0c", "Question": "According to the CIA, as of January 2015, the Minister of Interior of a particular country had the same surname as a Minister Without Portfolio of another country. Give the names of these two countries as a comma-separated list in alphabetical order, using the titles of their English Wikipedia pages as of August 2023.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0a5a5fd6-97b7-4cce-ad9f-bdaaee042bec", "Question": "Who directed the 1994 movie that shares a name with the unique vertex of the capsid in the abstract of the paper \"Distinct DNA Exit and Packaging Portals...\" on PLOS Biology?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f8ee2934-7981-4cd4-8abc-e91239c50c97", "Question": "In Google Street View, navigate to the bridge in Hershey, Pennsylvania where Park Avenue crosses Spring Creek. Up until 2023, on how many of the Street View dates was the monorail train visible on the overhead beam? Be sure to look in every direction that the beam is visible while standing on the bridge.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "a582f258-4e42-47db-8669-4280537fe223", "Question": "What colors are the original prototype logo of the 2019 TV show made by the company that had the most viewed YouTube video at the end of 2020? Return the colors in a comma-separated, alphabetical list.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f7f36b51-3ad4-4149-af93-1b4355b438b0", "Question": "If you ride the Washington D.C. metro orange line from Vienna to New Carrollton in 2022, how many transfer stations would you pass through? Your answer should be a numerical whole value. ", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d31b3ec0-bf91-42dd-82fb-43655964505d", "Question": "According to the USGS NAS database, how many instances of Ichthyomyzon unicuspis were found in the Hudson River between the years 1980 and 2000?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c47d5037-f591-4958-a27c-c9c89dc30df0", "Question": "Today is September 1, 2023. Sometime last month, a frequent guest of the Joe Rogan Experience podcast had an application for judicial review dismissed by a panel of judges in Canada. As of today, how many times has this guest appeared on the Joe Rogan Experience podcast? Please report your answer as an integer value.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "ebfd308e-59c1-4389-8812-7ea4cda7f4bc", "Question": "A paperweight production factory has a series of goals that are A through H as listed below.\n\nObtain raw materials.\nFind investors.\nBrainstorm new paperweights.\nOptimize production efficiency.\nOpen additional plants.\nUpgrade old machinery.\nDo performance reviews.\nHire more workers.\n\nThe goals have a hierarchy of importance. Opening additional plants is more important than obtaining raw materials and brainstorming new paperweights, upgrading old machinery is more important than one other goal only, and the manager thinks that optimizing production efficiency is less important than brainstorming new paperweights but more important than obtaining raw materials but has the order backwards. Hiring new workers is more important than three other goals, doing performance reviews is more important than hiring new workers but less important than optimizing production efficiency, and finding investors is less important than obtaining raw materials. What is the second most important goal (return it exactly as given in the question)?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "8c638aa7-81d9-4d06-9ae4-0c0a5540ea5b", "Question": "Between August 31st of 2016 and August 31st of 2020, how much of an increase in range did Symmetra's Primary Fire gain in centimeters? Don't include units.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "2648baca-4e35-4353-8772-4da848c8440e", "Question": "In the AVS article from Sallander in 2001, how many more dog questions were there than owner questions in the first table?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "c70c16aa-6c86-4ad0-b2f6-82fc748f49a2", "Question": "The Wikipedia editor who first corrected \"\u0627\u062c\u0631\u0627\u0628\u06cc\" on the Farsi language version of the Product manager entry has a userpage. The next edit to his user page after making this edit was not made by him. According to that user's Babel template in July 2022, what language do they speak besides English at a near native level? Answer with the complete title of the English language Wikipedia article for the language as it would have been in July 2022.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "021a5339-744f-42b7-bd9b-9368b3efda7a", "Question": "The attached file shows a list of books in the collection of Scribe County Public Library. How many of the scenes that are in I Spy books in this library\u2019s collection are also in \u201cI Spy Ultimate Challenger\u201d?", "Level": 2, "file_name": "021a5339-744f-42b7-bd9b-9368b3efda7a.pdf", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "eb0292b5-a09a-451b-8cd1-a8671095a0d0", "Question": "I was watching this YouTube video the other day. The thumbnail had this yellow cartoon character in it. Who is this character? The YouTube channel was called Not Just Bikes, and I was watching the video on June 19th or 20th when it was newly uploaded (and the year is 2023). Just tell me the character\u2019s first name.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "67f89b20-03eb-4f2d-8cbc-a94e5cc05343", "Question": "Take the price of premium gasoline at the gas station with the address of 3053 N Ocean Blvd, Fort Lauderdale, Florida 33308, as shown in the Google Street View as of July 2018. Round down to two decimal places (i.e. ignore the 9/10 of a cent), and multiply by 100. Then multiply this by the number of O\u2019s in the picture closest to the top of the page (in the right-hand sidebar) on the Wikipedia page for \u201cTic-tac-toe\u201d as of June 4th, 2023.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "aea1ea38-dfd0-41ab-ad79-badc3c69c784", "Question": "The code in this file is an incomplete snippet written in a language that is named after something. What letter is it missing from its namesake's name?", "Level": 2, "file_name": "aea1ea38-dfd0-41ab-ad79-badc3c69c784.txt", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0870ef1f-c712-4e67-8940-8581cc9455dd", "Question": "According to the company's Twitter, what is the \"birthday\" of the company (in dd/mm/yyyy format) that can replace your Brass Rat if you lose yours? Assume you graduated 11 years before Jeff Goldblum played the character named Sophisticated Urbanite.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e8719a2f-6bbd-40b6-95b5-7706f7a6445a", "Question": "According to the US Bureau of Reclamation glossary, what does the acronym that shares its name with a book of the New Testament stand for?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4c386922-5c65-4f33-86d5-107a97ad6654", "Question": "In the abstract of the Lithuanian Bulvi\u0173 rinka 2009 metais - Ingrida Luko\u0161iut\u0117, how many times does the author mention the year that the book \"The Propitious Esculent\" was published?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "fa93f13c-5967-41ad-9ccb-6a351be9f625", "Question": "If you look left of North on Google Maps for the coordinates 46.4192624,14.642093 with the pictures taken May 2021, what shape is the major sign on the right of the road?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b74aa3b0-16a7-4a79-a8df-0cbdcf1d2eb8", "Question": "How many more references in the latest 2022 wikipedia article on chocolate vs. the latest from 2021?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9e72b135-afa1-4e39-9e21-ea34d0726e20", "Question": "On CORE.ac.uk, what was the animal type that underwent the second highest percentage of radiographies in the late-2018 thesis paper from the Universidade de Evora about psittacines? Use the plural form of the type.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b4395888-4e6d-4d02-b7ee-11dbccdb802d", "Question": "What were the 4 unique characters that differentiated the architecture names of the image segmentation technique used to develop the novel deep learning model PDDCNN as an alphabetized, comma-separated list?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "088d34d3-8b99-4928-ba26-9a0c6098a616", "Question": "How far apart in million of meters (rounded to one decimal place) are the locations of the universities of the two excellent examples given in the first paragraph of An Online Edition of the Achilleid of Statius? For each university, use the point indicated by the coordinates on the university's Wikipedia page as of July 2023.", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4b52450e-0c0d-434e-8358-85e80cb2514c", "Question": "How many heirs of the Emperor of the Ming dynasty died as a result of the worst explosive accident with the highest death toll in recorded history? Please provide your response as a number.", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "b9c62d30-86f8-4b36-80b6-afb34852cea6", "Question": "According to Federal Reserve Economic Data (FRED) from the St. Louis Fed, prior to 2021-09, what was the most recent 3-month period where the percent change at an annual rate for the seasonally adjusted Median Consumer Price Index was over 5% for each month? Give your answer in the format YYYY-MM, YYYY-MM, where the starting month of the period comes before the ending month.", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0c393561-dd13-4b7c-ac49-20ac469aa276", "Question": "If I break all the floor tiles shown in this video and I can buy them in sets of 5 for $40, how many dollars will I have to spend to replace all the tiles if I also have to get grout that will set 2 tiles each tube for $9 a tube?", "Level": 3, "file_name": "0c393561-dd13-4b7c-ac49-20ac469aa276.MOV", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "9ca9bc20-1a0b-4076-9937-7724e3491cf8", "Question": "Let's take a look at the checker's board in the image I shared with you. In the image, it's currently red's turn. If the red player's move captures as many blue pieces as possible, how many blue pieces will be in a position to capture at least one red piece on blue's next turn?", "Level": 2, "file_name": "9ca9bc20-1a0b-4076-9937-7724e3491cf8.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d8434132-f196-4048-82c3-c06facff53c0", "Question": "The attached file shows a list of books that one of my students read, along with each book\u2019s genre and a rating of how well the student liked it. I am using this list to determine their favorite genre. What seems to be the student\u2019s favorite genre, based on the average rating that the student gave to books in that genre? If two genres are tied for highest average rating, use alphabetical order and separate them with a comma.", "Level": 2, "file_name": "d8434132-f196-4048-82c3-c06facff53c0.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "f6d29ef1-0e4d-41cb-ac25-e60023b3bd96", "Question": "The attached spreadsheet shows the occupants of the Liminal Springs Mall. The occupants are divided by \u201czone\u201d, based on where they\u2019re located in the mall. What is the number of the zone that has the highest total revenue? Just give the number.", "Level": 2, "file_name": "f6d29ef1-0e4d-41cb-ac25-e60023b3bd96.xlsx", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "4cf4a5c1-7c9c-4cce-94cb-57b8be196244", "Question": "In a remote village, a person's wealth in the afterlife is determined by the worth of the years of the zodiac during which they lived. The worth of each zodiac animal is given in the image. All occurrences of the birth year are counted in triple, and all occurrences of the death year are subtracted. All occurrences of the other years they lived in are added. A villager was born in 1925 and passed away in 1976. What was their wealth in the afterlife in coins, according to the villagers' beliefs? Just give the number.", "Level": 2, "file_name": "4cf4a5c1-7c9c-4cce-94cb-57b8be196244.png", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "e395eb42-8b78-4995-9f8b-25e3a56ab359", "Question": "In version 2.14 of the LaTeX xcolor module, how many more colors include \"blue\" in the name in the largest non-base color option than the smallest one?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "70e0a9c6-24bf-48ed-afa1-f0d0eaaa0209", "Question": "As of July 2023, what is the fourth letter of the administrative district Szyman\u00f3wek lies in?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "de21a804-9b9e-40b6-aede-392e3360b147", "Question": "What fingerprint has the third highest agriculture and biology weight on the Research@WUR Wageningen University & Research page for the 2009 paper from Gildemacher, Kaguongo, and Woldegiorgis?", "Level": 1, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "d0e1cd7d-0af2-4ca0-8e9d-7a3fa0687e7f", "Question": "Between 2000 and 2022, how many times was the english Wikipedia page for Fiji Mermaids edited on April Fool\u2019s Day?", "Level": 2, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}
{"task_id": "0-0-0-0-0", "Question": "Train an AI assistant able to solve GAIA (see https://huggingface.co/gaia-benchmark).", "Level": 3, "file_name": "", "Final answer": "?", "Annotator Metadata": {"Steps": "", "Number of steps": "", "How long did this take?": "", "Tools": "", "Number of tools": ""}}