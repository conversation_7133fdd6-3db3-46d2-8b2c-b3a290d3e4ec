# -----------------------------------------------------------------------------
# Supported Model Index  •  Convenience helper
#
# The canonical model-config mapping lives in `model_config.py` and is ~2000
# lines long. Navigating that file just to see whether a model key exists was
# getting painful, so this lightweight companion keeps **only** the keys in a
# flat list so you can:
#
#   •  skim the supported models at a glance;
#   •  hit ⌘/Ctrl-F and jump straight to the one you need;
#   •  import the list in quick scripts/tests without hauling in the whole
#      config (e.g. `if model_name in SUPPORTED_MODELS:`).
# -----------------------------------------------------------------------------

SUPPORTED_MODELS = [
    "gorilla-openfunctions-v2",
    "DeepSeek-R1-0528",
    "DeepSeek-R1-0528-FC",
    "DeepSeek-V3-0324-FC",
    "gpt-4.5-preview-2025-02-27",
    "gpt-4.5-preview-2025-02-27-FC",
    "gpt-4.1-2025-04-14-FC",
    "gpt-4.1-2025-04-14",
    "gpt-4.1-mini-2025-04-14-FC",
    "gpt-4.1-mini-2025-04-14",
    "gpt-4.1-nano-2025-04-14-FC",
    "gpt-4.1-nano-2025-04-14",
    "o1-2024-12-17-FC",
    "o1-2024-12-17",
    "o3-mini-2025-01-31-FC",
    "o3-mini-2025-01-31",
    "gpt-4o-2024-11-20",
    "gpt-4o-2024-11-20-FC",
    "gpt-4o-mini-2024-07-18",
    "gpt-4o-mini-2024-07-18-FC",
    "claude-3-opus-20240229",
    "claude-3-opus-20240229-FC",
    "claude-3-7-sonnet-20250219",
    "claude-3-7-sonnet-20250219-FC",
    "claude-3-5-sonnet-20241022",
    "claude-3-5-sonnet-20241022-FC",
    "claude-3-5-haiku-20241022",
    "claude-3-5-haiku-20241022-FC",
    "nova-pro-v1.0",
    "nova-lite-v1.0",
    "nova-micro-v1.0",
    "open-mistral-nemo-2407",
    "open-mistral-nemo-2407-FC",
    "mistral-large-2411",
    "mistral-large-2411-FC",
    "mistral-small-2503",
    "mistral-small-2503-FC",
    "mistral-medium-2505",
    "mistral-medium-2505-FC",
    "firefunction-v2-FC",
    "Nexusflow-Raven-v2",
    "gemini-2.0-flash-lite-001-FC",
    "gemini-2.0-flash-lite-001",
    "gemini-2.0-flash-001-FC",
    "gemini-2.0-flash-001",
    "gemini-2.5-pro-preview-05-06-FC",
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.0-flash-thinking-exp-01-21",
    "meetkai/functionary-small-v3.1-FC",
    "meetkai/functionary-medium-v3.1-FC",
    "databricks-dbrx-instruct",
    "command-r-plus-FC",
    "command-r7b-12-2024-FC",
    "command-a-03-2025-FC",
    "snowflake/arctic",
    "nvidia/llama-3.1-nemotron-ultra-253b-v1",
    "nvidia/nemotron-4-340b-instruct",
    "BitAgent/GoGoAgent",
    "palmyra-x-004",
    "grok-3-beta-FC",
    "grok-3-beta",
    "grok-3-mini-beta-FC",
    "grok-3-mini-beta",
    "qwen3-0.6b-FC",
    "qwen3-0.6b",
    "qwen3-1.7b-FC",
    "qwen3-1.7b",
    "qwen3-4b-FC",
    "qwen3-4b",
    "qwen3-8b-FC",
    "qwen3-8b",
    "qwen3-14b-FC",
    "qwen3-14b",
    "qwen3-32b-FC",
    "qwen3-32b",
    "qwen3-30b-a3b-FC",
    "qwen3-30b-a3b",
    "qwen3-235b-a22b-FC",
    "qwen3-235b-a22b",
    "qwq-32b-FC",
    "qwq-32b",
    "xiaoming-14B",
    "DM-Cito-8B",
    "deepseek-ai/DeepSeek-R1",
    "google/gemma-3-1b-it",
    "google/gemma-3-4b-it",
    "google/gemma-3-12b-it",
    "google/gemma-3-27b-it",
    "meta-llama/Llama-3.1-8B-Instruct-FC",
    "meta-llama/Llama-3.1-8B-Instruct",
    "meta-llama/Llama-3.1-70B-Instruct-FC",
    "meta-llama/Llama-3.1-70B-Instruct",
    "meta-llama/Llama-3.2-1B-Instruct-FC",
    "meta-llama/Llama-3.2-3B-Instruct-FC",
    "meta-llama/Llama-3.3-70B-Instruct-FC",
    "meta-llama/Llama-4-Scout-17B-16E-Instruct-FC",
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8-FC",
    "Salesforce/Llama-xLAM-2-70b-fc-r",
    "Salesforce/Llama-xLAM-2-8b-fc-r",
    "Salesforce/xLAM-2-32b-fc-r",
    "Salesforce/xLAM-2-3b-fc-r",
    "Salesforce/xLAM-2-1b-fc-r",
    "mistralai/Ministral-8B-Instruct-2410",
    "microsoft/phi-4",
    "microsoft/Phi-4-mini-instruct",
    "microsoft/Phi-4-mini-instruct-FC",
    "ibm-granite/granite-20b-functioncalling",
    "MadeAgents/Hammer2.1-7b",
    "MadeAgents/Hammer2.1-3b",
    "MadeAgents/Hammer2.1-1.5b",
    "MadeAgents/Hammer2.1-0.5b",
    "THUDM/glm-4-9b-chat",
    "Qwen/Qwen3-0.6B-FC",
    "Qwen/Qwen3-0.6B",
    "Qwen/Qwen3-1.7B-FC",
    "Qwen/Qwen3-1.7B",
    "Qwen/Qwen3-4B-FC",
    "Qwen/Qwen3-4B",
    "Qwen/Qwen3-8B-FC",
    "Qwen/Qwen3-8B",
    "Qwen/Qwen3-14B-FC",
    "Qwen/Qwen3-14B",
    "Qwen/Qwen3-32B-FC",
    "Qwen/Qwen3-32B",
    "Qwen/Qwen3-30B-A3B-FC",
    "Qwen/Qwen3-30B-A3B",
    "Qwen/Qwen3-235B-A22B-FC",
    "Qwen/Qwen3-235B-A22B",
    "Team-ACE/ToolACE-2-8B",
    "openbmb/MiniCPM3-4B",
    "openbmb/MiniCPM3-4B-FC",
    "watt-ai/watt-tool-8B",
    "watt-ai/watt-tool-70B",
    "ZJared/Haha-7B",
    "speakleash/Bielik-11B-v2.3-Instruct",
    "NovaSky-AI/Sky-T1-32B-Preview",
    "tiiuae/Falcon3-10B-Instruct-FC",
    "tiiuae/Falcon3-7B-Instruct-FC",
    "tiiuae/Falcon3-3B-Instruct-FC",
    "tiiuae/Falcon3-1B-Instruct-FC",
    "uiuc-convai/CoALM-8B",
    "uiuc-convai/CoALM-70B",
    "uiuc-convai/CoALM-405B",
    "BitAgent/BitAgent-8B",
    "ThinkAgents/ThinkAgent-1B",
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-novita",
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-FC-novita",
    "meta-llama/llama-4-scout-17b-16e-instruct-novita",
    "meta-llama/llama-4-scout-17b-16e-instruct-FC-novita",
    "qwen/qwq-32b-FC-novita",
    "qwen/qwq-32b-novita",
    "Ling/ling-lite-v1.5",
]
