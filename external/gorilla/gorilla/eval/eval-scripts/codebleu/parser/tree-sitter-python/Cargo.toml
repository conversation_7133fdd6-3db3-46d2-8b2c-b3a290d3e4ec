[package]
name = "tree-sitter-python"
description = "Python grammar for the tree-sitter parsing library"
version = "0.20.2"
authors = [
    "<PERSON> <maxbrun<PERSON><EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
license = "MIT"
readme = "bindings/rust/README.md"
keywords = ["incremental", "parsing", "python"]
categories = ["parsing", "text-editors"]
repository = "https://github.com/tree-sitter/tree-sitter-python"
edition = "2018"

build = "bindings/rust/build.rs"
include = [
  "bindings/rust/*",
  "grammar.js",
  "queries/*",
  "src/*",
]

[lib]
path = "bindings/rust/lib.rs"

[dependencies]
tree-sitter = ">= 0.19, < 0.21"

[build-dependencies]
cc = "1.0"
