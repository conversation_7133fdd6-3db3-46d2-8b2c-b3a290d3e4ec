{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@uiw/codemirror-theme-material": "^4.23.2", "@uiw/react-codemirror": "^4.23.2", "@vercel/analytics": "^1.3.1", "ace-builds": "^1.35.2", "ansi_up": "^6.0.2", "axios": "^1.7.2", "bootstrap": "^5.3.3", "bootstrap-dark": "^1.0.3", "bootswatch": "^5.3.3", "dompurify": "^3.1.6", "react": "^18.0.0", "react-ace": "^12.0.0", "react-bootstrap": "^2.10.4", "react-dom": "^18.0.0", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.0.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-terminal-ui": "^1.3.0", "react-toastify": "^10.0.5", "sanitize-html": "^2.13.0", "styled-components": "^6.1.12", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}