# External Benchmarks Directory

This directory contains the original source code for external benchmark libraries that are integrated into agenteval.

## Directory Structure

```
external/
├── README.md                           # This file
├── tau-bench/                          # Tau-Bench benchmark
│   ├── tau_bench/                      # Core tau-bench modules
│   ├── run.py                          # Main entry point
│   ├── setup.py                        # Installation script
│   └── ...                             # Other tau-bench files
└── berkeley-function-call-leaderboard/ # Berkeley Function Calling Leaderboard
    ├── bfcl_eval/                      # Core BFC modules
    ├── data/                           # Test data
    ├── openfunctions_evaluation.py     # Legacy entry point
    └── ...                             # Other BFC files
```

## Purpose

### Why External Directory?

1. **Unified Management**: All benchmark code is contained within the agenteval project
2. **Clear Separation**: Distinguishes between agenteval core code and external benchmarks
3. **Version Control**: Easier to manage different versions of benchmark code
4. **Path Simplification**: No need for complex relative paths like `../`
5. **Deployment Friendly**: Simplifies containerization and deployment

### Integration Approach

- **Preserved Integrity**: Original benchmark code is kept unchanged
- **Dynamic Loading**: Benchmarks are loaded dynamically through adapters
- **Dependency Isolation**: Each benchmark's dependencies are isolated
- **Graceful Degradation**: Missing dependencies don't break the system

## Benchmark Details

### Tau-Bench

**Description**: A comprehensive benchmark for evaluating LLM agents in interactive environments.

**Key Features**:
- Multiple environments (retail, airline)
- Various agent strategies (tool-calling, react, act, few-shot)
- User simulation for realistic interactions
- Comprehensive evaluation metrics

**Dependencies**:
- litellm
- pydantic
- Model provider SDKs (openai, anthropic, etc.)

**Usage via agenteval**:
```python
# Via API
{
    "benchmark": "tau_bench",
    "model": "gpt-4o",
    "params": {
        "env": "retail",
        "agent_strategy": "tool-calling",
        "num_trials": 3
    }
}
```

### Berkeley Function Calling Leaderboard (BFC)

**Description**: A comprehensive evaluation framework for LLM function calling capabilities.

**Key Features**:
- Multiple test categories (simple, parallel, multi-turn, etc.)
- AST and executable evaluation
- Comprehensive scoring metrics
- Support for various model backends

**Dependencies**:
- numpy
- pandas
- typer
- tabulate
- Model-specific dependencies

**Usage via agenteval**:
```python
# Via API
{
    "benchmark": "bfc",
    "model": "gpt-4o", 
    "params": {
        "test_category": ["simple", "parallel"],
        "temperature": 0.001
    }
}
```

## Installation

### Option 1: Core Only (Recommended for Development)
```bash
# Install only agenteval core
pip install -r requirements.txt

# Benchmarks will show limited functionality warnings
# but adapters will still work for parameter validation, etc.
```

### Option 2: Full Installation
```bash
# Install agenteval core
pip install -r requirements.txt

# Install tau-bench dependencies
cd external/tau-bench
pip install -r requirements.txt  # if exists
pip install openai anthropic mistralai google-generativeai pydantic

# Install BFC dependencies
cd ../gorilla/berkeley-function-call-leaderboard
pip install numpy pandas typer tabulate

# Return to agenteval root
cd ../..
```

### Option 3: Selective Installation
```bash
# Install only specific benchmark dependencies
pip install openai anthropic mistralai google-generativeai pydantic  # for tau-bench only
# OR
pip install numpy pandas typer  # for BFC only
```

## Development Guidelines

### Adding New Benchmarks

1. **Place Source Code**: Add the benchmark source code to `external/`
2. **Create Adapter**: Implement a new adapter in `src/adapters/`
3. **Update Documentation**: Add benchmark details to this README
4. **Test Integration**: Verify integration with debug scripts

### Updating Benchmarks

1. **Backup**: Always backup the current version before updating
2. **Test Compatibility**: Ensure adapters still work with new versions
3. **Update Dependencies**: Check for new dependency requirements
4. **Version Documentation**: Document version changes

### Best Practices

- **Don't Modify**: Never modify the original benchmark source code
- **Use Adapters**: All integration logic should be in adapters
- **Handle Dependencies**: Always handle missing dependencies gracefully
- **Document Changes**: Keep this README updated with any changes

## Troubleshooting

### Common Issues

1. **Import Errors**: Usually due to missing dependencies
   - Solution: Install benchmark-specific dependencies
   - Fallback: Adapters will show warnings but continue to work

2. **Path Issues**: Benchmark code not found
   - Check that benchmark directories exist in `external/`
   - Verify adapter configuration uses correct paths

3. **Version Conflicts**: Different benchmarks require conflicting dependencies
   - Use virtual environments for isolation
   - Consider containerized deployment

### Getting Help

- Check adapter logs for detailed error messages
- Run debug scripts to verify setup
- Refer to individual benchmark documentation in their directories

## License

Each benchmark in this directory retains its original license. Please refer to the LICENSE files in individual benchmark directories for specific terms.
