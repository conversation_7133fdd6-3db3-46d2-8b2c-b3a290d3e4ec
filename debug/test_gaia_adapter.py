#!/usr/bin/env python3
"""Test script for GAIA adapter."""

import asyncio
import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.adapters.gaia.gaia import GAIAAdapter
from src.benchmark_config.gaia import GAIAConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_gaia_adapter():
    """Test the GAIA adapter."""
    
    # Test configuration
    config = {
        "tasks_path": "./external/gaia-tasks",
        "framework": "autogen"
    }
    
    # Create adapter
    adapter = GAIAAdapter(config)
    
    # Test parameters
    params = {
        "model": "gpt-3.5-turbo",
        "framework": "autogen",
        "max_steps": 5,
        "timeout": 300,
        "num_tasks": 2,  # Test with just 2 tasks
        "level": 1  # Test with level 1 tasks only
    }
    
    try:
        logger.info("Testing GAIA adapter parameter validation...")
        validated_params = await adapter.validate_params(params)
        logger.info(f"Validated parameters: {validated_params}")
        
        logger.info("Testing GAIA adapter initialization...")
        logger.info(f"Number of tasks loaded: {len(adapter.tasks)}")
        
        if adapter.tasks:
            logger.info("Sample task:")
            sample_task = adapter.tasks[0]
            for key, value in sample_task.items():
                if isinstance(value, str) and len(value) > 100:
                    logger.info(f"  {key}: {value[:100]}...")
                else:
                    logger.info(f"  {key}: {value}")
        
        # Note: We won't run the full execution here as it requires proper inference backends
        logger.info("GAIA adapter test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error testing GAIA adapter: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(test_gaia_adapter())
