# AgentEval Debug 快速入门

> 🎯 **30秒开始调试** - 最简单的AgentEval调试入门指南

## 🚀 立即开始

### 第一步：环境检查
```bash
python debug/scripts/setup_debug.py
```

### 第二步：开始调试
```bash
code .  # 打开VSCode
# 按 Ctrl+Shift+D → 选择 "Debug Tau-Bench Script (Direct Test)" → 按 F5
```

## 📋 调试配置选择

| 我想调试... | 选择这个配置 | 用时 |
|------------|-------------|------|
| Adapter逻辑问题 | Debug Tau-Bench Script (Direct Test) | 1-2分钟 |
| BFC Adapter逻辑 | Debug BFC Script (Direct Test) | 1-2分钟 |
| Worker处理流程 | Debug Tau-Bench via AgentEval | 3-5分钟 |
| 完整API流程 | Debug Tau-Bench Script (API Test) | 5-10分钟 |
| API服务器问题 | Debug API Server | 2-3分钟 |

## 🛠️ 常用命令

```bash
# 环境检查
python debug/scripts/setup_debug.py

# 最简单的调试
python debug/scripts/debug_tau_bench.py --test-direct
python debug/scripts/debug_bfc.py --test-direct
python debug/scripts/debug_gaia.py --test-direct

# 测试新的BFC inference backend集成
python debug/scripts/debug_bfc.py --test-backend

# 测试BFC的FC模式
python debug/scripts/debug_bfc.py --test-direct --use-fc-mode

# 检查配置
python debug/scripts/setup_debug.py
```

## 🚨 遇到问题？

### 常见问题快速解决

```bash
# Python解释器找不到
which python  # 复制路径到 .vscode/launch.json

# 环境变量未设置
export OPENAI_API_KEY="your_key_here"

# 端口被占用
lsof -i :8000 && kill -9 <PID>

# 外部依赖缺失
git submodule update --init --recursive
```

## 📚 完整文档

**需要详细指南？** 查看 **[DEBUG_GUIDE.md](DEBUG_GUIDE.md)** 获取：

- 🔧 详细环境配置
- 🎯 所有调试配置说明
- 🛠️ 高级调试技巧
- 🧪 API与工作器调试
- 📋 最佳实践和学习路径

## 📁 文件结构

```
agenteval/
├── DEBUG_GUIDE.md               # 📖 完整调试指南
├── DEBUG_README.md              # 📋 本快速入门（你在这里）
├── .vscode/
│   ├── launch.json              # VSCode调试配置
│   └── tasks.json               # VSCode任务配置
└── debug/
    ├── scripts/
    │   ├── debug_tau_bench.py   # Tau-Bench调试脚本
    │   ├── debug_bfc.py         # BFC调试脚本
    │   ├── debug_gaia.py        # GAIA调试脚本
    │   └── setup_debug.py       # 环境检查脚本
    └── vscode/
        ├── launch.json          # 调试配置备份
        └── tasks.json           # 任务配置备份
```

---

**开始您的调试之旅！** 🚀

> 💡 **提示**：从最简单的Direct Test开始，逐步深入。遇到问题先查看 [DEBUG_GUIDE.md](DEBUG_GUIDE.md)！
