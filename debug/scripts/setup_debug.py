#!/usr/bin/env python3
"""Setup script for AgentEval debug environment."""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional


def check_python_environment():
    """Check if the Python environment is correctly set up."""
    print("🐍 Checking Python environment...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print(f"❌ Python {python_version.major}.{python_version.minor} is not supported. Please use Python 3.8+")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
    else:
        print("⚠️  No virtual environment detected. Consider using one.")
    
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    print("📦 Checking dependencies...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "requests",
        "pydantic",
        "yaml",
        "asyncio",
        "logging"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True


def check_external_repos():
    """Check if external repositories are properly set up."""
    print("📁 Checking external repositories...")

    # When running from VSCode debug, cwd is set to workspaceFolder (agenteval)
    external_dir = Path("external")
    if not external_dir.exists():
        print("❌ external/ directory not found")
        return False
    
    # Check tau-bench
    tau_bench_dir = external_dir / "tau-bench"
    if tau_bench_dir.exists():
        print("✅ tau-bench repository found")
    else:
        print("❌ tau-bench repository not found")
        print("Run: git submodule update --init --recursive")
        return False
    
    # Check berkeley-function-call-leaderboard
    bfc_dir = external_dir / "berkeley-function-call-leaderboard"
    if bfc_dir.exists():
        print("✅ berkeley-function-call-leaderboard repository found")
    else:
        print("❌ berkeley-function-call-leaderboard repository not found")
        print("Run: git submodule update --init --recursive")
        return False
    
    return True


def check_config_files():
    """Check if configuration files exist."""
    print("⚙️  Checking configuration files...")

    config_dir = Path("config")
    if not config_dir.exists():
        print("❌ config/ directory not found")
        return False
    
    required_configs = [
        "server.yaml",
        "worker.yaml"
    ]
    
    missing_configs = []
    
    for config_file in required_configs:
        config_path = config_dir / config_file
        if config_path.exists():
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file} (missing)")
            missing_configs.append(config_file)
    
    if missing_configs:
        print(f"\n⚠️  Missing config files: {', '.join(missing_configs)}")
        return False
    
    return True


def check_vscode_config():
    """Check if VSCode debug configuration is set up."""
    print("🔧 Checking VSCode configuration...")

    vscode_dir = Path(".vscode")
    if not vscode_dir.exists():
        print("❌ .vscode/ directory not found")
        return False
    
    launch_json = vscode_dir / "launch.json"
    if launch_json.exists():
        print("✅ launch.json found")
        
        # Check if our debug configurations are present
        try:
            with open(launch_json, 'r') as f:
                config = json.load(f)
            
            config_names = [c.get('name', '') for c in config.get('configurations', [])]
            
            required_configs = [
                "Debug Tau-Bench via AgentEval",
                "Debug BFC via AgentEval",
                "Debug Tau-Bench Script (Direct Test)",
                "Debug BFC Script (Direct Test)"
            ]
            
            missing_debug_configs = []
            for req_config in required_configs:
                if req_config in config_names:
                    print(f"✅ {req_config}")
                else:
                    print(f"❌ {req_config} (missing)")
                    missing_debug_configs.append(req_config)
            
            if missing_debug_configs:
                print(f"\n⚠️  Missing debug configurations: {', '.join(missing_debug_configs)}")
                return False
                
        except json.JSONDecodeError:
            print("❌ launch.json is not valid JSON")
            return False
    else:
        print("❌ launch.json not found")
        return False
    
    tasks_json = vscode_dir / "tasks.json"
    if tasks_json.exists():
        print("✅ tasks.json found")
    else:
        print("❌ tasks.json not found")
        return False
    
    return True


def check_environment_variables():
    """Check if required environment variables are set."""
    print("🔑 Checking environment variables...")
    
    required_env_vars = [
        "OPENAI_API_KEY",
        "ANTHROPIC_API_KEY",
        "GOOGLE_API_KEY",
        "MISTRAL_API_KEY"
    ]
    
    missing_env_vars = []
    
    for env_var in required_env_vars:
        if os.getenv(env_var):
            print(f"✅ {env_var}")
        else:
            print(f"❌ {env_var} (not set)")
            missing_env_vars.append(env_var)
    
    if missing_env_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_env_vars)}")
        print("Set them in your shell or create a .env file")
        return False
    
    return True


def check_debug_scripts():
    """Check if debug scripts exist and are executable."""
    print("📜 Checking debug scripts...")

    debug_scripts = [
        "debug/scripts/debug_tau_bench.py",
        "debug/scripts/debug_bfc.py"
    ]

    for script in debug_scripts:
        script_path = Path(script)
        if script_path.exists():
            print(f"✅ {script}")
            # Make executable
            os.chmod(script_path, 0o755)
        else:
            print(f"❌ {script} (missing)")
            return False

    return True


def run_quick_test():
    """Run a quick test to verify the setup."""
    print("🧪 Running quick test...")

    try:
        # Add project root to path for imports
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent.parent.parent))

        # Test importing core modules
        from src.engine.config import get_config
        from src.adapters.tau_bench import TauBenchAdapter
        from src.adapters.bfc import BFCAdapter

        print("✅ Core modules import successfully")

        # Test configuration loading
        config = get_config()
        print("✅ Configuration loads successfully")

        return True
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False


def main():
    """Main setup check function."""
    print("🚀 AgentEval Debug Environment Setup Check")
    print("=" * 50)
    
    checks = [
        ("Python Environment", check_python_environment),
        ("Dependencies", check_dependencies),
        ("External Repositories", check_external_repos),
        ("Configuration Files", check_config_files),
        ("VSCode Configuration", check_vscode_config),
        ("Environment Variables", check_environment_variables),
        ("Debug Scripts", check_debug_scripts),
        ("Quick Test", run_quick_test)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        print("-" * len(check_name))
        
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 All checks passed! Your debug environment is ready.")
        print("\nNext steps:")
        print("1. Open VSCode in this directory")
        print("2. Press Ctrl+Shift+D to open the Debug panel")
        print("3. Select a debug configuration and start debugging!")
        print("\nRefer to debug/docs/DEBUG.md for detailed usage instructions.")
    else:
        print("❌ Some checks failed. Please fix the issues above before debugging.")
        print("\nRefer to debug/docs/DEBUG.md for setup instructions.")
        sys.exit(1)


if __name__ == "__main__":
    main()
