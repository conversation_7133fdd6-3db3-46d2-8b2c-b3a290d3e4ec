#!/usr/bin/env python3
"""Debug script for Tau-Bench evaluation via AgentEval framework."""

import os
import sys
import asyncio
import logging
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

import requests

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.engine.config import get_config
from src.adapters.tau_bench import TauBenchAdapter

logger = logging.getLogger(__name__)


def wait_for_api_server(api_url: str, max_wait: int = 60) -> bool:
    """Wait for API server to be ready.

    Args:
        api_url: API server URL
        max_wait: Maximum wait time in seconds

    Returns:
        True if server is ready, False otherwise
    """
    print(f"⏳ Waiting for API server at {api_url} to be ready (max {max_wait}s)...")
    start_time = time.time()

    # Give the server some initial time to start up
    # In compound debug mode, give extra time for API server to start
    compound_mode = os.environ.get("COMPOUND_DEBUG_MODE", "false").lower() == "true"
    initial_wait = 15 if compound_mode else 5
    print(f"⏳ Giving API server initial startup time ({initial_wait}s)...")
    time.sleep(initial_wait)

    while time.time() - start_time < max_wait:
        try:
            print(f"⏳ Checking API server at {api_url}/api/stats...")
            response = requests.get(f"{api_url}/api/stats", timeout=10)
            if response.status_code == 200:
                print("✅ API server is ready!")
                return True
            else:
                print(f"⏳ API server responded with status {response.status_code}, waiting...")
        except requests.exceptions.ConnectionError as e:
            print(f"⏳ Connection failed: {e}, API server not ready yet...")
        except requests.exceptions.Timeout as e:
            print(f"⏳ Request timeout: {e}, API server not ready yet...")
        except requests.exceptions.RequestException as e:
            print(f"⏳ Request error: {e}, API server not ready yet...")

        elapsed = time.time() - start_time
        remaining = max_wait - elapsed
        print(f"⏳ Waiting... ({elapsed:.1f}s elapsed, {remaining:.1f}s remaining)")
        time.sleep(3)

    print("❌ API server failed to start within timeout")
    return False


def submit_tau_bench_task(
    api_url: str = "http://localhost:8000",
    model: str = "gpt-4o",
    env: str = "retail",
    agent_strategy: str = "tool-calling",
    num_trials: int = 1,
    task_split: str = "test",
    start_index: int = 0,
    end_index: int = 2,
    **kwargs
) -> Optional[str]:
    """Submit a Tau-Bench task to the API server.
    
    Args:
        api_url: API server URL
        model: Model name
        env: Environment (retail/airline)
        agent_strategy: Agent strategy
        num_trials: Number of trials
        task_split: Task split (train/test/dev)
        start_index: Start index
        end_index: End index
        **kwargs: Additional parameters
        
    Returns:
        Task ID if successful, None otherwise
    """
    task_data = {
        "benchmark": "tau_bench",
        "model": model,
        "params": {
            "env": env,
            "agent_strategy": agent_strategy,
            "num_trials": num_trials,
            "task_split": task_split,
            "start_index": start_index,
            "end_index": end_index,
            **kwargs
        }
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/tasks",
            json=task_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Task submitted successfully: {task_id}")
        print(f"📊 Task details: {json.dumps(result, indent=2)}")
        return task_id
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to submit task: {e}")
        return None


def check_task_status(api_url: str, task_id: str) -> Optional[Dict[str, Any]]:
    """Check task status.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        
    Returns:
        Task status if successful, None otherwise
    """
    try:
        response = requests.get(
            f"{api_url}/api/tasks/{task_id}",
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        print(f"📋 Task status: {result.get('status', 'unknown')}")
        return result
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to check task status: {e}")
        return None


def wait_for_completion(api_url: str, task_id: str, timeout: int = 600) -> bool:
    """Wait for task completion.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        timeout: Timeout in seconds
        
    Returns:
        True if completed successfully, False otherwise
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        status = check_task_status(api_url, task_id)
        if not status:
            return False
            
        task_status = status.get("status", "unknown")
        
        if task_status == "completed":
            print("✅ Task completed successfully!")
            return True
        elif task_status == "failed":
            print("❌ Task failed!")
            error = status.get("error", "Unknown error")
            print(f"Error: {error}")
            return False
        elif task_status in ["pending", "running"]:
            print(f"⏳ Task {task_status}... waiting...")
            time.sleep(10)
        else:
            print(f"❓ Unknown status: {task_status}")
            time.sleep(5)
    
    print("⏰ Timeout waiting for task completion")
    return False


async def test_adapter_directly():
    """Test the Tau-Bench adapter directly."""
    print("🧪 Testing Tau-Bench adapter directly...")

    # Load configuration from worker.yaml
    # When running from VSCode debug, cwd is set to workspaceFolder (agenteval)
    worker_config_path = "config/worker.yaml"
    if not Path(worker_config_path).exists():
        print(f"❌ Worker configuration file not found: {worker_config_path}")
        return False

    config = get_config(worker_config_path)

    # Initialize adapter
    adapter = TauBenchAdapter(config.get("benchmarks.tau_bench", {}))
    
    # Test parameters
    params = {
        "model": "Qwen/Qwen3-8B",
        "model_provider": "local",
        "user_model_provider": "openai",
        "env": "retail",
        "agent_strategy": "tool-calling",
        "num_trials": 1,
        "task_split": "test",
        "start_index": 0,
        "end_index": 1
    }
    
    try:
        print(f"📝 Testing with params: {json.dumps(params, indent=2)}")
        result = await adapter.execute(params)
        print(f"✅ Direct adapter test successful!")

        # Create a JSON-serializable version of the result
        serializable_result = {}
        for key, value in result.items():
            try:
                json.dumps(value)  # Test if value is serializable
                serializable_result[key] = value
            except (TypeError, ValueError):
                serializable_result[key] = str(value)  # Convert to string if not serializable

        print(f"📊 Result: {json.dumps(serializable_result, indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Direct adapter test failed: {e}")
        logger.exception("Direct adapter test failed")
        return False


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Debug Tau-Bench evaluation")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API server URL")
    parser.add_argument("--model", default="gpt-4o", help="Model name")
    parser.add_argument("--env", default="retail", choices=["retail", "airline"], help="Environment")
    parser.add_argument("--agent-strategy", default="tool-calling", help="Agent strategy")
    parser.add_argument("--num-trials", type=int, default=1, help="Number of trials")
    parser.add_argument("--task-split", default="test", choices=["train", "test", "dev"], help="Task split")
    parser.add_argument("--start-index", type=int, default=0, help="Start index")
    parser.add_argument("--end-index", type=int, default=2, help="End index")
    parser.add_argument("--test-direct", action="store_true", help="Test adapter directly")
    parser.add_argument("--submit-only", action="store_true", help="Submit task only, don't wait")
    parser.add_argument("--timeout", type=int, default=600, help="Timeout for waiting")
    parser.add_argument("--skip-server-wait", action="store_true", help="Skip waiting for API server")
    parser.add_argument("--server-wait-timeout", type=int, default=60, help="Timeout for waiting for API server to start")
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Starting Tau-Bench debug session...")
    
    if args.test_direct:
        print("🧪 Running direct adapter test...")
        success = asyncio.run(test_adapter_directly())
        if not success:
            sys.exit(1)
        return
    
    # Wait for API server to be ready (unless skipped)
    if not args.skip_server_wait:
        if not wait_for_api_server(args.api_url, args.server_wait_timeout):
            print("❌ API server is not ready")
            sys.exit(1)

    # Submit task via API
    print("📤 Submitting Tau-Bench task via API...")
    task_id = submit_tau_bench_task(
        api_url=args.api_url,
        model=args.model,
        env=args.env,
        agent_strategy=args.agent_strategy,
        num_trials=args.num_trials,
        task_split=args.task_split,
        start_index=args.start_index,
        end_index=args.end_index
    )
    
    if not task_id:
        print("❌ Failed to submit task")
        sys.exit(1)
    
    if args.submit_only:
        print(f"✅ Task submitted: {task_id}")
        return
    
    # Wait for completion
    print("⏳ Waiting for task completion...")
    success = wait_for_completion(args.api_url, task_id, args.timeout)
    
    if success:
        print("🎉 Debug session completed successfully!")
    else:
        print("❌ Debug session failed or timed out")
        sys.exit(1)


if __name__ == "__main__":
    main()
