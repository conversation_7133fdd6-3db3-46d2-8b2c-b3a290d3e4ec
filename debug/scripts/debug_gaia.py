#!/usr/bin/env python3
"""Debug script for GAIA evaluation via AgentEval framework."""

import os
import sys
import asyncio
import logging
import argparse
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional

import requests

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.engine.config import get_config
from src.adapters.gaia.gaia import GAIAAdapter

logger = logging.getLogger(__name__)


def wait_for_api_server(api_url: str, max_wait: int = 60) -> bool:
    """Wait for API server to be ready.

    Args:
        api_url: API server URL
        max_wait: Maximum wait time in seconds

    Returns:
        True if server is ready, False otherwise
    """
    print(f"⏳ Waiting for API server at {api_url} to be ready (max {max_wait}s)...")
    start_time = time.time()

    # Give the server some initial time to start up
    # In compound debug mode, give extra time for API server to start
    compound_mode = os.environ.get("COMPOUND_DEBUG_MODE", "false").lower() == "true"
    initial_wait = 15 if compound_mode else 5
    print(f"⏳ Giving API server initial startup time ({initial_wait}s)...")
    time.sleep(initial_wait)

    while time.time() - start_time < max_wait:
        try:
            print(f"⏳ Checking API server at {api_url}/api/stats...")
            response = requests.get(f"{api_url}/api/stats", timeout=10)
            if response.status_code == 200:
                print("✅ API server is ready!")
                return True
            else:
                print(f"⏳ API server responded with status {response.status_code}, waiting...")
        except requests.exceptions.ConnectionError as e:
            print(f"⏳ Connection failed: {e}, API server not ready yet...")
        except requests.exceptions.Timeout as e:
            print(f"⏳ Request timeout: {e}, API server not ready yet...")
        except requests.exceptions.RequestException as e:
            print(f"⏳ Request error: {e}, API server not ready yet...")

        elapsed = time.time() - start_time
        remaining = max_wait - elapsed
        print(f"⏳ Waiting... ({elapsed:.1f}s elapsed, {remaining:.1f}s remaining)")
        time.sleep(3)

    print("❌ API server failed to start within timeout")
    return False


def submit_gaia_task(
    api_url: str = "http://localhost:8000",
    model: str = "gpt-4o",
    level: Optional[int] = 1,
    num_tasks: Optional[int] = 2,
    max_steps: int = 10,
    timeout: int = 300,
    **kwargs
) -> Optional[str]:
    """Submit a GAIA task to the API server.
    
    Args:
        api_url: API server URL
        model: Model name
        level: GAIA difficulty level (1-3 or None for all)
        num_tasks: Number of tasks to run
        max_steps: Maximum steps per task
        timeout: Timeout per task in seconds
        **kwargs: Additional parameters
        
    Returns:
        Task ID if successful, None otherwise
    """
    task_data = {
        "benchmark": "gaia",
        "model": model,
        "params": {
            "level": level,
            "num_tasks": num_tasks,
            "max_steps": max_steps,
            "timeout": timeout,
            **kwargs
        }
    }
    
    try:
        response = requests.post(
            f"{api_url}/api/tasks",
            json=task_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        task_id = result.get("task_id")
        print(f"✅ Task submitted successfully: {task_id}")
        print(f"📊 Task details: {json.dumps(result, indent=2)}")
        return task_id
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to submit task: {e}")
        return None


def check_task_status(api_url: str, task_id: str) -> Optional[Dict[str, Any]]:
    """Check task status.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        
    Returns:
        Task status if successful, None otherwise
    """
    try:
        response = requests.get(
            f"{api_url}/api/tasks/{task_id}",
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        print(f"📋 Task status: {result.get('status', 'unknown')}")
        return result
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to check task status: {e}")
        return None


def wait_for_completion(api_url: str, task_id: str, timeout: int = 600) -> bool:
    """Wait for task completion.
    
    Args:
        api_url: API server URL
        task_id: Task ID
        timeout: Timeout in seconds
        
    Returns:
        True if completed successfully, False otherwise
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        status = check_task_status(api_url, task_id)
        if not status:
            return False
            
        task_status = status.get("status", "unknown")
        
        if task_status == "completed":
            print("✅ Task completed successfully!")
            return True
        elif task_status == "failed":
            print("❌ Task failed!")
            error = status.get("error", "Unknown error")
            print(f"Error: {error}")
            return False
        elif task_status in ["pending", "running"]:
            print(f"⏳ Task {task_status}... waiting...")
            time.sleep(10)
        else:
            print(f"❓ Unknown status: {task_status}")
            time.sleep(5)
    
    print("⏰ Timeout waiting for task completion")
    return False


async def test_adapter_directly():
    """Test the GAIA adapter directly."""
    print("🧪 Testing GAIA adapter directly...")

    # Load configuration from worker.yaml
    # When running from VSCode debug, cwd is set to workspaceFolder (agenteval)
    worker_config_path = "config/worker.yaml"
    if not Path(worker_config_path).exists():
        print(f"❌ Worker configuration file not found: {worker_config_path}")
        return False

    config = get_config(worker_config_path)

    # Initialize adapter
    adapter = GAIAAdapter(config.get("benchmarks.gaia", {}))

    # Test parameters
    params = {
        "model": "Qwen/Qwen3-8B",
        "model_provider": "local",
        "user_model_provider": "openai",
        "level": 1,
        "num_tasks": 1,
        "max_steps": 5,
        "timeout": 300,
        "temperature": 0.0,
        "max_tokens": 2048
    }

    try:
        print(f"📝 Testing with params: {json.dumps(params, indent=2)}")

        # First test parameter validation
        print("🔍 Testing parameter validation...")
        validated_params = await adapter.validate_params(params)
        print(f"✅ Parameter validation successful!")
        print(f"📋 Validated params: {json.dumps(validated_params, indent=2)}")

        # Check if tasks are loaded
        print(f"📊 Number of GAIA tasks loaded: {len(adapter.tasks)}")
        if len(adapter.tasks) == 0:
            print("⚠️  No GAIA tasks loaded. Please check external/gaia/2023 directory.")
            return False

        # Show sample task info
        if adapter.tasks:
            sample_task = adapter.tasks[0]
            print("📋 Sample task info:")
            for key, value in sample_task.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")

        # Test execute method core logic
        print("\n🔧 Testing execute method core logic...")
        await test_execute_core_logic(adapter, validated_params)

        # Test full execute method if service is available
        print("\n🌐 Testing full execute method (if service is available)...")
        await test_full_execute_method(adapter, validated_params)

        print("✅ Direct adapter test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Direct adapter test failed: {e}")
        logger.exception("Direct adapter test failed")
        return False


async def test_execute_core_logic(adapter, params):
    """Test the core logic of the execute method without requiring a running service."""
    print("🧪 Testing execute method core components...")

    # Test 1: Task filtering by level
    print("🔍 Testing task filtering by level...")
    original_tasks = adapter.tasks.copy()

    # Filter tasks based on level
    level = params.get("level")
    tasks_to_run = original_tasks
    if level is not None and level != "all":
        tasks_to_run = [task for task in original_tasks if task.get("level") == level]

    print(f"📊 Original tasks: {len(original_tasks)}")
    print(f"📊 Tasks after level filtering (level={level}): {len(tasks_to_run)}")

    # Test 2: Task limiting
    print("🔍 Testing task limiting...")
    if params.get("num_tasks"):
        tasks_to_run = tasks_to_run[:params["num_tasks"]]

    print(f"📊 Tasks after limiting (num_tasks={params.get('num_tasks')}): {len(tasks_to_run)}")

    # Test 3: LLM configuration retrieval (using adapter method)
    print("🔍 Testing LLM configuration retrieval...")
    try:
        model_name = params["model"]
        llm_config = adapter._get_llm_config_for_model(model_name)
        print(f"✅ LLM config retrieved for model '{model_name}':")
        print(f"📋 LLM config keys: {list(llm_config.keys())}")
    except Exception as e:
        print(f"❌ Failed to get LLM config: {e}")
        llm_config = {}

    # Test 4: Request payload preparation (using adapter's internal logic)
    print("🔍 Testing request payload preparation...")
    if tasks_to_run:
        sample_task = tasks_to_run[0]
        task_id = sample_task.get("task_id", "")

        try:
            # Test the payload preparation logic that's used in _send_single_query
            # This mirrors the actual implementation without duplicating code
            print(f"✅ Testing payload preparation logic for task '{task_id}':")
            print(f"📋 Task question preview: {sample_task.get('question', '')[:100]}...")
            print(f"📋 Task level: {sample_task.get('level', 1)}")
            print(f"📋 Task file_name: {sample_task.get('file_name', 'None')}")
            print(f"📋 Task file_path: {sample_task.get('file_path', 'None')}")
            print(f"📋 LLM config available: {len(llm_config) > 0}")
            print(f"📋 Max steps: {params.get('max_steps', 15)}")
            print(f"📋 Timeout: {params.get('timeout', 600)}")

        except Exception as e:
            print(f"❌ Failed to test payload preparation: {e}")

    # Test 5: Results aggregation (using adapter method)
    print("🔍 Testing results aggregation...")
    try:
        # Create mock results for aggregation testing using the same format as adapter
        mock_results = []
        for i, task in enumerate(tasks_to_run[:3]):  # Test with up to 3 tasks
            mock_results.append({
                "task_id": task.get("task_id", f"task_{i}"),
                "question": task.get("question", ""),
                "level": task.get("level", 1),
                "predicted_answer": f"Mock answer {i}",
                "ground_truth": task.get("final_answer", ""),
                "score": 0.8 if i % 2 == 0 else 0.2,  # Alternate between high and low scores
                "is_correct": i % 2 == 0,
                "trajectory": [],
                "metrics": {}
            })

        # Test aggregation using adapter's method
        aggregated_results = adapter._process_execution_results(mock_results, params)

        print(f"✅ Results aggregation successful:")
        print(f"📋 Aggregated result keys: {list(aggregated_results.keys())}")
        print(f"📊 Total tasks: {aggregated_results.get('total_tasks', 0)}")
        print(f"📊 Accuracy: {aggregated_results.get('accuracy', 0.0):.2f}")
        print(f"📊 Level breakdown: {aggregated_results.get('level_breakdown', {})}")

    except Exception as e:
        print(f"❌ Failed to aggregate results: {e}")

    # Test 6: Test adapter's execute method logic flow (without service calls)
    print("🔍 Testing execute method logic flow...")
    try:
        if tasks_to_run:
            sample_task = tasks_to_run[0]

            # Test the exact logic that execute() uses for task preparation
            print(f"✅ Execute method task preparation logic:")
            print(f"📋 Sample task ID: {sample_task.get('task_id', 'N/A')}")
            print(f"📋 Sample task level: {sample_task.get('level', 'N/A')}")
            print(f"📋 Sample task question length: {len(sample_task.get('question', ''))}")

            # Test what would be sent to _send_queries_to_service
            print(f"📋 Tasks that would be sent to service: {len(tasks_to_run)}")
            print(f"� Max concurrent requests: {params.get('max_concurrent_requests', adapter.max_concurrent_requests)}")

            # This validates that the execute method's preparation logic works correctly
            print(f"✅ Execute method preparation logic validated")
            print(f"💡 Service communication testing requires a running GAIA service")

    except Exception as e:
        print(f"❌ Failed to test execute method logic: {e}")

    print("🎉 Execute method core logic testing completed!")


async def test_full_execute_method(adapter, params):
    """Test the full execute method if service is available."""
    print("🧪 Testing full execute method...")

    # Check if service is available
    service_url = adapter.service_url
    print(f"🔍 Checking service availability at {service_url}...")

    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{service_url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    print("✅ Service is available!")

                    # Test with a very small subset to avoid long execution
                    test_params = params.copy()
                    test_params["num_tasks"] = 1
                    test_params["max_steps"] = 2
                    test_params["timeout"] = 60

                    print(f"🚀 Running full execute method with limited params...")
                    print(f"📋 Test params: num_tasks=1, max_steps=2, timeout=60")

                    try:
                        results = await adapter.execute(test_params)
                        print("✅ Full execute method completed successfully!")
                        print(f"📊 Results summary:")
                        print(f"   - Total tasks: {results.get('total_tasks', 0)}")
                        print(f"   - Accuracy: {results.get('accuracy', 0.0):.2f}")
                        print(f"   - Benchmark: {results.get('benchmark', 'N/A')}")
                        print(f"   - Model: {results.get('model', 'N/A')}")

                        if results.get('results'):
                            sample_result = results['results'][0]
                            print(f"📋 Sample result:")
                            print(f"   - Task ID: {sample_result.get('task_id', 'N/A')}")
                            print(f"   - Score: {sample_result.get('score', 0.0)}")
                            print(f"   - Is correct: {sample_result.get('is_correct', False)}")
                            print(f"   - Predicted answer: {sample_result.get('predicted_answer', 'N/A')[:50]}...")

                    except Exception as e:
                        print(f"❌ Full execute method failed: {e}")
                        print("💡 This might be expected if the service is not properly configured")

                else:
                    print(f"⚠️  Service returned status {response.status}, skipping full execute test")

    except Exception as e:
        print(f"⚠️  Service not available ({e}), skipping full execute test")
        print("💡 To test full execute method, start the GAIA service first")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Debug GAIA evaluation")
    parser.add_argument("--api-url", default="http://localhost:8000", help="API server URL")
    parser.add_argument("--model", default="gpt-4o", help="Model name")
    parser.add_argument("--level", type=int, choices=[1, 2, 3], help="GAIA difficulty level (1-3)")
    parser.add_argument("--level-all", action="store_true", help="Run all difficulty levels")
    parser.add_argument("--num-tasks", type=int, default=2, help="Number of tasks to run")
    parser.add_argument("--max-steps", type=int, default=10, help="Maximum steps per task")
    parser.add_argument("--timeout", type=int, default=300, help="Timeout per task in seconds")
    parser.add_argument("--temperature", type=float, default=0.0, help="Model temperature")
    parser.add_argument("--max-tokens", type=int, default=4096, help="Maximum tokens")
    parser.add_argument("--test-direct", action="store_true", help="Test adapter directly")
    parser.add_argument("--submit-only", action="store_true", help="Submit task only, don't wait")
    parser.add_argument("--wait-timeout", type=int, default=1200, help="Timeout for waiting for completion")
    parser.add_argument("--skip-server-wait", action="store_true", help="Skip waiting for API server")
    parser.add_argument("--server-wait-timeout", type=int, default=60, help="Timeout for waiting for API server to start")
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("🚀 Starting GAIA debug session...")

    if args.test_direct:
        print("🧪 Running direct adapter test...")
        success = asyncio.run(test_adapter_directly())
        if not success:
            sys.exit(1)
        return

    # Determine level parameter
    level = None
    if args.level_all:
        level = "all"
    elif args.level:
        level = args.level
    else:
        level = 1  # Default to level 1

    # Wait for API server to be ready (unless skipped)
    if not args.skip_server_wait:
        if not wait_for_api_server(args.api_url, args.server_wait_timeout):
            print("❌ API server is not ready")
            sys.exit(1)

    # Submit task via API
    print("📤 Submitting GAIA task via API...")
    task_id = submit_gaia_task(
        api_url=args.api_url,
        model=args.model,
        level=level,
        num_tasks=args.num_tasks,
        max_steps=args.max_steps,
        timeout=args.timeout,
        temperature=args.temperature,
        max_tokens=args.max_tokens
    )

    if not task_id:
        print("❌ Failed to submit task")
        sys.exit(1)

    if args.submit_only:
        print(f"✅ Task submitted: {task_id}")
        return

    # Wait for completion
    print("⏳ Waiting for task completion...")
    success = wait_for_completion(args.api_url, task_id, args.wait_timeout)

    if success:
        print("🎉 Debug session completed successfully!")
    else:
        print("❌ Debug session failed or timed out")
        sys.exit(1)


if __name__ == "__main__":
    main()
