"""Base class for inference backends."""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any

logger = logging.getLogger(__name__)


class InferenceBackend(ABC):
    """Abstract base class for inference backends."""

    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize the inference backend.

        Args:
            name: Backend name (e.g., 'openai', 'anthropic', 'local').
            config: Backend configuration from worker.yaml endpoints.
        """
        self.name = name
        self.config = config
        self.api_key = config.get("api_key", "")
        self.base_url = config.get("url", "")
        self.models = config.get("models", [])
        self.max_concurrent = config.get("max_concurrent", 5)

    @abstractmethod
    def completion(self, **kwargs) -> Any:
        """Perform completion inference.

        Args:
            **kwargs: Completion parameters (messages, model, temperature, etc.).

        Returns:
            Native completion response from the LLM provider.
        """
        pass

    def supports_model(self, model: str) -> bool:
        """Check if this backend supports the given model."""
        return model in self.models or "*" in self.models
