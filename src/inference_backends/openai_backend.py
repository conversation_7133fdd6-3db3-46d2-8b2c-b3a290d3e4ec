"""OpenAI inference backend."""

import logging
from typing import Dict, Any

from .base import InferenceBackend

logger = logging.getLogger(__name__)


class OpenAIBackend(InferenceBackend):
    """OpenAI inference backend using native OpenAI client."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("openai", config)
        self._client = None

    @property
    def client(self):
        """Lazy initialization of OpenAI client."""
        if self._client is None:
            try:
                from openai import OpenAI
                self._client = OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url if self.base_url else None
                )
            except ImportError:
                raise ImportError("OpenAI library not installed. Run: pip install openai>=1.76.0")
        return self._client

    def completion(self, **kwargs) -> Any:
        """Perform OpenAI completion and return native response."""
        try:
            # Extract parameters
            messages = kwargs.get("messages", [])
            model = kwargs.get("model", "gpt-3.5-turbo")
            temperature = kwargs.get("temperature", 0.95)

            # Prepare OpenAI API call parameters
            api_params = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
            }

            # Add optional parameters
            if "top_p" in kwargs:
                api_params["top_p"] = kwargs["top_p"]
            if "frequency_penalty" in kwargs:
                api_params["frequency_penalty"] = kwargs["frequency_penalty"]
            if "presence_penalty" in kwargs:
                api_params["presence_penalty"] = kwargs["presence_penalty"]
            if "stop" in kwargs:
                api_params["stop"] = kwargs["stop"]
            if "tools" in kwargs:
                api_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                api_params["tool_choice"] = kwargs["tool_choice"]

            logger.debug(f"OpenAI completion with model: {model}")
            response = self.client.chat.completions.create(**api_params)

            # Add tau-bench compatibility
            response._hidden_params = {
                "response_cost": 0.0  # Placeholder for cost calculation
            }

            return response

        except Exception as e:
            logger.error(f"OpenAI completion failed: {e}")
            raise
