"""Google inference backend."""

import logging
from typing import Dict, Any

from .base import InferenceBackend

logger = logging.getLogger(__name__)


class GoogleBackend(InferenceBackend):
    """Google inference backend that returns OpenAI-compatible responses."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("google", config)
        self._client = None

    @property
    def client(self):
        """Lazy initialization of Google client."""
        if self._client is None:
            try:
                import google.generativeai as genai
                genai.configure(api_key=self.api_key)
                self._client = genai
            except ImportError:
                raise ImportError("Google Generative AI library not installed. Run: pip install google-generativeai>=0.5.4")
        return self._client

    def completion(self, **kwargs) -> Any:
        """Perform Google completion and return OpenAI-compatible response."""
        try:
            from types import SimpleNamespace

            # Extract parameters
            messages = kwargs.get("messages", [])
            model_name = kwargs.get("model", "gemini-pro")
            temperature = kwargs.get("temperature", 0.95)
            max_tokens = kwargs.get("max_tokens", 32768)
            tools = kwargs.get("tools", [])

            # Initialize model
            model = self.client.GenerativeModel(model_name)

            # Convert messages to Google format
            prompt_parts = []
            for msg in messages:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role == "system":
                    prompt_parts.append(f"System: {content}")
                elif role == "user":
                    prompt_parts.append(f"User: {content}")
                elif role == "assistant":
                    prompt_parts.append(f"Assistant: {content}")

            # Add tools information to prompt if available
            if tools:
                tools_desc = "Available tools:\n"
                for tool in tools:
                    if tool.get("type") == "function":
                        func = tool.get("function", {})
                        tools_desc += f"- {func.get('name', '')}: {func.get('description', '')}\n"
                prompt_parts.insert(0, tools_desc)

            prompt = "\n".join(prompt_parts)

            # Configure generation parameters
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
            }

            if "top_p" in kwargs:
                generation_config["top_p"] = kwargs["top_p"]
            if "top_k" in kwargs:
                generation_config["top_k"] = kwargs["top_k"]

            logger.debug(f"Google completion with model: {model_name}, tools: {len(tools)}")
            google_response = model.generate_content(
                prompt,
                generation_config=generation_config
            )

            # Create OpenAI-compatible response structure
            content = google_response.text if google_response.text else ""

            message = SimpleNamespace()
            message.content = content
            message.tool_calls = None  # Google tool calling not implemented yet
            message.role = "assistant"

            # Add model_dump method for tau-bench compatibility
            def model_dump():
                result = {"role": "assistant", "content": message.content}
                return result
            message.model_dump = model_dump

            choice = SimpleNamespace()
            choice.message = message
            choice.finish_reason = "stop"

            response = SimpleNamespace()
            response.choices = [choice]
            response.model = model_name
            response.id = f"google-{hash(prompt) % 1000000}"
            response.usage = SimpleNamespace()
            response.usage.prompt_tokens = 0  # Not available
            response.usage.completion_tokens = 0  # Not available
            response.usage.total_tokens = 0  # Not available

            # Add tau-bench compatibility
            response._hidden_params = {
                "response_cost": 0.0  # Placeholder
            }

            return response

        except Exception as e:
            logger.error(f"Google completion failed: {e}")
            raise
