"""Anthropic inference backend."""

import logging
from typing import Dict, Any

from .base import InferenceBackend

logger = logging.getLogger(__name__)


class AnthropicBackend(InferenceBackend):
    """Anthropic inference backend that returns OpenAI-compatible responses."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("anthropic", config)
        self._client = None

    @property
    def client(self):
        """Lazy initialization of Anthropic client."""
        if self._client is None:
            try:
                from anthropic import Anthropic
                self._client = Anthropic(
                    api_key=self.api_key,
                    base_url=self.base_url if self.base_url else None
                )
            except ImportError:
                raise ImportError("Anthropic library not installed. Run: pip install anthropic>=0.49.0")
        return self._client

    def completion(self, **kwargs) -> Any:
        """Perform Anthropic completion and return OpenAI-compatible response."""
        try:
            import json
            from types import SimpleNamespace

            # Extract parameters
            messages = kwargs.get("messages", [])
            model = kwargs.get("model", "claude-3-sonnet-20240229")
            temperature = kwargs.get("temperature", 0.95)
            tools = kwargs.get("tools", [])

            # Convert OpenAI format messages to Anthropic format
            anthropic_messages = []
            system_message = None

            for msg in messages:
                if msg.get("role") == "system":
                    system_message = msg.get("content", "")
                else:
                    anthropic_messages.append({
                        "role": msg.get("role", "user"),
                        "content": msg.get("content", "")
                    })

            # Prepare Anthropic API call parameters
            api_params = {
                "model": model,
                "messages": anthropic_messages,
                "temperature": temperature,
            }

            if system_message:
                api_params["system"] = system_message

            # Add tools support for Anthropic
            if tools:
                anthropic_tools = []
                for tool in tools:
                    if tool.get("type") == "function":
                        func = tool.get("function", {})
                        anthropic_tools.append({
                            "name": func.get("name", ""),
                            "description": func.get("description", ""),
                            "input_schema": func.get("parameters", {})
                        })

                if anthropic_tools:
                    api_params["tools"] = anthropic_tools

            # Add optional parameters
            if "top_p" in kwargs:
                api_params["top_p"] = kwargs["top_p"]
            if "stop_sequences" in kwargs:
                api_params["stop_sequences"] = kwargs["stop_sequences"]
            elif "stop" in kwargs:
                api_params["stop_sequences"] = kwargs["stop"] if isinstance(kwargs["stop"], list) else [kwargs["stop"]]

            logger.debug(f"Anthropic completion with model: {model}, tools: {len(tools)}")
            anthropic_response = self.client.messages.create(**api_params)

            # Convert to OpenAI-compatible format
            content = ""
            tool_calls = None

            if anthropic_response.content:
                for content_block in anthropic_response.content:
                    if content_block.type == "text":
                        content = content_block.text
                    elif content_block.type == "tool_use":
                        if tool_calls is None:
                            tool_calls = []
                        tool_calls.append({
                            "id": content_block.id,
                            "type": "function",
                            "function": {
                                "name": content_block.name,
                                "arguments": json.dumps(content_block.input) if content_block.input else "{}"
                            }
                        })

            # Create OpenAI-compatible response structure
            message = SimpleNamespace()
            message.content = content if content else None
            message.tool_calls = tool_calls
            message.role = "assistant"

            # Add model_dump method for tau-bench compatibility
            def model_dump():
                result = {"role": "assistant"}
                if message.content is not None:
                    result["content"] = message.content
                if message.tool_calls is not None:
                    result["tool_calls"] = message.tool_calls
                return result
            message.model_dump = model_dump

            choice = SimpleNamespace()
            choice.message = message
            choice.finish_reason = anthropic_response.stop_reason or "stop"

            response = SimpleNamespace()
            response.choices = [choice]
            response.model = anthropic_response.model
            response.id = anthropic_response.id
            response.usage = SimpleNamespace()
            response.usage.prompt_tokens = anthropic_response.usage.input_tokens if anthropic_response.usage else 0
            response.usage.completion_tokens = anthropic_response.usage.output_tokens if anthropic_response.usage else 0
            response.usage.total_tokens = response.usage.prompt_tokens + response.usage.completion_tokens

            # Add tau-bench compatibility
            response._hidden_params = {
                "response_cost": 0.0  # Placeholder
            }

            return response

        except Exception as e:
            logger.error(f"Anthropic completion failed: {e}")
            raise
