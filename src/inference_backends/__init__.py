"""Inference backends package.

This package provides a minimal interface for different LLM providers,
returning their native completion responses directly to benchmarks.
"""

# Import all classes and the global manager instance
from .base import InferenceBackend
from .openai_backend import OpenAIBackend
from .anthropic_backend import AnthropicBackend
from .local_backend import LocalBackend
from .google_backend import GoogleBackend
from .manager import InferenceBackendManager, backend_manager

# Export all public classes and instances
__all__ = [
    "InferenceBackend",
    "OpenAIBackend", 
    "AnthropicBackend",
    "LocalBackend",
    "GoogleBackend",
    "InferenceBackendManager",
    "backend_manager",
]
