"""Local/custom inference backend."""

import logging
from typing import Dict, Any

from .base import InferenceBackend

logger = logging.getLogger(__name__)


class LocalBackend(InferenceBackend):
    """Local/custom inference backend using OpenAI-compatible API."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("local", config)
        self._client = None

    @property
    def client(self):
        """Lazy initialization of OpenAI client for local endpoint."""
        if self._client is None:
            try:
                from openai import OpenAI
                if not self.base_url:
                    raise ValueError("Local backend requires a base_url in configuration")

                self._client = OpenAI(
                    api_key=self.api_key or "local-key",  # Some local servers require any non-empty key
                    base_url=self.base_url
                )
            except ImportError:
                raise ImportError("OpenAI library not installed. Run: pip install openai>=1.76.0")
        return self._client

    def completion(self, **kwargs) -> Any:
        """Perform local completion using OpenAI-compatible API."""
        try:
            # Extract parameters
            messages = kwargs.get("messages", [])
            model = kwargs.get("model", "local-model")
            temperature = kwargs.get("temperature", 0.95)

            # Prepare API call parameters
            api_params = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
            }

            # Add optional parameters
            if "top_p" in kwargs:
                api_params["top_p"] = kwargs["top_p"]
            if "frequency_penalty" in kwargs:
                api_params["frequency_penalty"] = kwargs["frequency_penalty"]
            if "presence_penalty" in kwargs:
                api_params["presence_penalty"] = kwargs["presence_penalty"]
            if "stop" in kwargs:
                api_params["stop"] = kwargs["stop"]
            if "tools" in kwargs:
                api_params["tools"] = kwargs["tools"]
            if "tool_choice" in kwargs:
                api_params["tool_choice"] = kwargs["tool_choice"]

            logger.debug(f"Local completion with model: {model}, base_url: {self.base_url}")
            response = self.client.chat.completions.create(**api_params)

            # Add tau-bench compatibility
            response._hidden_params = {
                "response_cost": 0.0  # Placeholder
            }

            return response

        except Exception as e:
            logger.error(f"Local completion failed: {e}")
            raise
