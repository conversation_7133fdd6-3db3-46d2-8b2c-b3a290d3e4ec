"""
Base benchmark configuration classes.

This module provides the base configuration class that all benchmark adapters
should inherit from, ensuring consistency and type safety.
"""

from pydantic import BaseModel
from typing import Any, Optional, Dict


class BaseBenchmarkConfig(BaseModel):
    """Base configuration class for all benchmark adapters.
    
    This class provides common functionality and ensures consistency
    across different benchmark implementations.
    """
    model: str
    inference_backend: Optional[Any] = None
    
    class Config:
        arbitrary_types_allowed = True
        
    def model_dump_serializable(self) -> Dict[str, Any]:
        """Serialize configuration, handling non-serializable objects.
        
        Returns:
            Dictionary with serializable configuration data.
        """
        config_dict = self.model_dump()
        if "inference_backend" in config_dict and config_dict["inference_backend"]:
            backend = config_dict["inference_backend"]
            if hasattr(backend, 'name'):
                config_dict["inference_backend"] = backend.name
            else:
                config_dict["inference_backend"] = str(backend)
        return config_dict
