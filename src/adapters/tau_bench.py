"""Tau-Bench adapter for the Agent Evaluation Tool."""

import sys
import asyncio
from typing import Dict, Any, List, Optional
import logging
from pathlib import Path

from src.adapters.base import BaseAdapter
from src.inference_backends import backend_manager
from src.benchmark_config.tau_bench import TauBenchConfig

logger = logging.getLogger(__name__)

class TauBenchAdapter(BaseAdapter):
    """Adapter for Tau-Bench benchmark."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the Tau-Bench adapter.

        Args:
            config: Adapter configuration.
        """
        super().__init__(config)
        self.repo_path = Path(config.get("repo_path", "./external/tau-bench"))
        self._tau_bench_run = None
        self._initialize_tau_bench()

    def _initialize_tau_bench(self) -> None:
        """Initialize Tau-Bench by importing its modules."""
        try:
            # Add Tau-Bench repo to Python path
            tau_bench_path = str(self.repo_path.resolve())
            if tau_bench_path not in sys.path:
                sys.path.insert(0, tau_bench_path)

            # Check if the repo exists
            if not self.repo_path.exists():
                logger.error(f"Tau-Bench repository not found at {self.repo_path}")
                return

            # Import Tau-Bench modules
            from tau_bench.run import run as tau_bench_run

            self._tau_bench_run = tau_bench_run

            logger.info(f"Successfully initialized Tau-Bench from {self.repo_path}")

        except ImportError as e:
            logger.warning(f"Failed to initialize Tau-Bench due to missing dependencies: {e}")
            logger.warning("Tau-Bench functionality will be limited. Please install tau-bench dependencies.")
        except Exception as e:
            logger.error(f"Failed to initialize Tau-Bench: {e}")
            raise

    def _get_inference_backend(self, provider: str) -> Optional[Any]:
        """Get inference backend for a specific provider.

        Args:
            provider: Provider name (e.g., 'openai', 'anthropic', 'local').

        Returns:
            Inference backend instance or None if not found.
        """
        try:
            backend = backend_manager.get_backend(provider.lower())
            if backend:
                logger.debug(f"Found backend '{backend.name}' for provider '{provider}'")
                return backend

            logger.warning(f"No backend found for provider '{provider}'")
            return None

        except Exception as e:
            logger.warning(f"Failed to get inference backend for provider '{provider}': {e}")
            return None

    def _get_inference_backend_by_model(self, model_name: str) -> Optional[Any]:
        """Get inference backend for a specific model by searching all endpoints.

        Args:
            model_name: Model name (e.g., 'gpt-4', 'Qwen/Qwen3-8B').

        Returns:
            Inference backend instance or None if not found.
        """
        try:
            # Search through all registered backends to find one that supports this model
            for backend_name, backend in backend_manager.backends.items():
                if hasattr(backend, 'models') and backend.models:
                    if model_name in backend.models:
                        logger.info(f"Found backend '{backend_name}' for model '{model_name}'")
                        return backend

            # If no exact match found, log available backends and models for debugging
            logger.warning(f"No backend found for model '{model_name}'")
            for backend_name, backend in backend_manager.backends.items():
                models = getattr(backend, 'models', [])
                logger.debug(f"Backend '{backend_name}' supports models: {models}")

            return None

        except Exception as e:
            logger.warning(f"Failed to get inference backend for model '{model_name}': {e}")
            return None
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a Tau-Bench evaluation.

        Args:
            params: Evaluation parameters.

        Returns:
            Evaluation results.
        """
        # Validate parameters
        params = await self.validate_params(params)

        try:
            if not self._tau_bench_run:
                raise RuntimeError("Tau-Bench not properly initialized. Please check dependencies and repository path.")

            # Get inference backend from worker.yaml configuration
            # First try to find backend by model name, then fall back to model_provider
            inference_backend = self._get_inference_backend_by_model(params["model"])
            if not inference_backend:
                inference_backend = self._get_inference_backend(params.get("model_provider", "openai"))

            # Create TauBenchConfig from validated parameters
            # Prepare config parameters, overriding specific values as needed
            config_params = params.copy()
            config_params["inference_backend"] = inference_backend
            # Keep backward compatibility with direct API parameters
            config_params["api_key"] = inference_backend.api_key if inference_backend else None
            config_params["base_url"] = inference_backend.base_url if inference_backend else None

            run_config = TauBenchConfig(**config_params)

            # Run tau-bench evaluation in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, self._tau_bench_run, run_config
            )

            # Process results
            return self._process_execution_results(results, params, run_config)

        except Exception as e:
            logger.error(f"Error executing Tau-Bench: {e}")
            raise

    def _process_execution_results(self, results: List[Any], params: Dict[str, Any],
                                  run_config: Any) -> Dict[str, Any]:
        """Process Tau-Bench execution results and calculate metrics.

        Args:
            results: Raw results from Tau-Bench execution.
            params: Execution parameters.
            run_config: Run configuration object.

        Returns:
            Processed results with metrics.
        """
        processed_results = []
        total_reward = 0.0
        total_tasks = len(results)

        for result in results:
            processed_results.append({
                "task_id": result.task_id,
                "reward": result.reward,
                "info": result.info,
                "trajectory": result.traj,
                "trial": result.trial
            })
            total_reward += result.reward

        # Calculate metrics
        avg_reward = total_reward / total_tasks if total_tasks > 0 else 0.0
        success_rate = sum(1 for r in results if r.reward >= 0.99) / total_tasks if total_tasks > 0 else 0.0

        # Create a serializable config dict
        config_dict = run_config.model_dump_serializable()

        return {
            "benchmark": "tau_bench",
            "model": params["model"],
            "env": params.get("env", "retail"),
            "agent_strategy": params.get("agent_strategy", "tool-calling"),
            "total_tasks": total_tasks,
            "avg_reward": avg_reward,
            "success_rate": success_rate,
            "results": processed_results,
            "config": config_dict
        }

    async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize Tau-Bench evaluation parameters using TauBenchConfig.

        Args:
            params: Evaluation parameters.

        Returns:
            Validated and normalized parameters.

        Raises:
            ValueError: If parameters are invalid.
        """
        try:
            # Create TauBenchConfig - this handles all validation, normalization, and defaults
            tau_config = TauBenchConfig(**params)

            # Return the validated parameters as a dictionary (excluding runtime-only fields)
            validated_params = tau_config.model_dump()
            # Remove runtime-only fields that are set in execute()
            for field in ["inference_backend", "api_key", "base_url"]:
                validated_params.pop(field, None)
            return validated_params

        except Exception as e:
            raise ValueError(f"Parameter validation failed: {str(e)}")
