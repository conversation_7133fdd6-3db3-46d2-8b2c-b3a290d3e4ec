"""Adapter registry for managing benchmark adapters."""

import logging
from typing import Dict, Any, Type, List, Optional
from src.adapters.base import BaseAdapter

logger = logging.getLogger(__name__)


class AdapterRegistry:
    """Registry for managing benchmark adapters."""
    
    def __init__(self):
        """Initialize the adapter registry."""
        self._adapter_classes: Dict[str, Type[BaseAdapter]] = {}
        self._adapter_instances: Dict[str, BaseAdapter] = {}
    
    def register(self, name: str, adapter_class: Type[BaseAdapter]) -> None:
        """Register an adapter class.
        
        Args:
            name: Adapter name.
            adapter_class: Adapter class.
        """
        if name in self._adapter_classes:
            logger.warning(f"Adapter '{name}' is already registered, overriding")
        
        self._adapter_classes[name] = adapter_class
        logger.debug(f"Registered adapter: {name}")
    
    def create_adapter(self, name: str, config: Dict[str, Any]) -> BaseAdapter:
        """Create an adapter instance.
        
        Args:
            name: Adapter name.
            config: Adapter configuration.
            
        Returns:
            Adapter instance.
            
        Raises:
            ValueError: If adapter is not registered.
        """
        if name not in self._adapter_classes:
            raise ValueError(f"Unknown adapter: {name}. Available adapters: {list(self._adapter_classes.keys())}")
        
        adapter_class = self._adapter_classes[name]
        adapter = adapter_class(config)
        
        # Cache the instance for reuse
        self._adapter_instances[name] = adapter
        logger.info(f"Created adapter instance: {name}")
        
        return adapter
    
    def get_adapter(self, name: str) -> Optional[BaseAdapter]:
        """Get a cached adapter instance.
        
        Args:
            name: Adapter name.
            
        Returns:
            Adapter instance or None if not found.
        """
        return self._adapter_instances.get(name)
    
    def list_adapters(self) -> List[str]:
        """List all registered adapter names.
        
        Returns:
            List of adapter names.
        """
        return list(self._adapter_classes.keys())
    
    def is_registered(self, name: str) -> bool:
        """Check if an adapter is registered.
        
        Args:
            name: Adapter name.
            
        Returns:
            True if registered, False otherwise.
        """
        return name in self._adapter_classes
    
    def unregister(self, name: str) -> bool:
        """Unregister an adapter.
        
        Args:
            name: Adapter name.
            
        Returns:
            True if unregistered, False if not found.
        """
        if name in self._adapter_classes:
            del self._adapter_classes[name]
            if name in self._adapter_instances:
                del self._adapter_instances[name]
            logger.info(f"Unregistered adapter: {name}")
            return True
        return False
    
    def clear(self) -> None:
        """Clear all registered adapters."""
        self._adapter_classes.clear()
        self._adapter_instances.clear()
        logger.info("Cleared all registered adapters")


# Global adapter registry instance
adapter_registry = AdapterRegistry()


def register_adapter(name: str, adapter_class: Type[BaseAdapter]) -> None:
    """Register an adapter globally.
    
    Args:
        name: Adapter name.
        adapter_class: Adapter class.
    """
    adapter_registry.register(name, adapter_class)


def get_adapter_registry() -> AdapterRegistry:
    """Get the global adapter registry.
    
    Returns:
        Global adapter registry instance.
    """
    return adapter_registry


# Global flag to track initialization
_adapters_initialized = False

def initialize_default_adapters() -> None:
    """Initialize default adapters (only once)."""
    global _adapters_initialized

    if _adapters_initialized:
        logger.debug("Default adapters already initialized, skipping")
        return

    try:
        from src.adapters.tau_bench import TauBenchAdapter
        register_adapter("tau_bench", TauBenchAdapter)
        logger.info("Registered tau_bench adapter")
    except ImportError as e:
        logger.warning(f"Failed to register tau_bench adapter: {e}")

    try:
        from src.adapters.bfc import BFCAdapter
        register_adapter("bfc", BFCAdapter)
        logger.info("Registered bfc adapter")
    except ImportError as e:
        logger.warning(f"Failed to register bfc adapter: {e}")

    try:
        from src.adapters.gaia.gaia import GAIAAdapter
        register_adapter("gaia", GAIAAdapter)
        logger.info("Registered gaia adapter")
    except ImportError as e:
        logger.warning(f"Failed to register gaia adapter: {e}")

    _adapters_initialized = True
    logger.info("Default adapters initialization completed")


def create_adapters_from_config(config) -> Dict[str, BaseAdapter]:
    """Create adapter instances from configuration.

    Args:
        config: Configuration object (WorkerConfig or dict).

    Returns:
        Dictionary mapping adapter names to instances.
    """
    adapters = {}
    benchmarks_config = config.get("benchmarks", {})
    
    for adapter_name in adapter_registry.list_adapters():
        adapter_config = benchmarks_config.get(adapter_name, {})
        
        # Check if adapter is enabled
        if adapter_config.get("enabled", True):
            try:
                adapter = adapter_registry.create_adapter(adapter_name, adapter_config)
                adapters[adapter_name] = adapter
                logger.info(f"Initialized {adapter_name} adapter")
            except Exception as e:
                logger.error(f"Failed to initialize {adapter_name} adapter: {e}")
        else:
            logger.info(f"Skipping disabled adapter: {adapter_name}")
    
    return adapters
