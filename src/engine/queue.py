"""Task queue implementation for the Agent Evaluation Tool."""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
import logging
import time
import sqlite3
import json
import aiosqlite
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    """Task status enum."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Task:
    """Task representation."""
    
    def __init__(self, task_id: str, task_type: str, params: Dict[str, Any]):
        """Initialize a task.
        
        Args:
            task_id: Unique task ID.
            task_type: Type of task (benchmark name).
            params: Task parameters.
        """
        self.task_id = task_id
        self.task_type = task_type
        self.params = params
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None
        self.created_at = time.time()
        self.started_at = None
        self.completed_at = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary.
        
        Returns:
            Dictionary representation of the task.
        """
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "params": self.params,
            "status": self.status,
            "result": self.result,
            "error": self.error,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at
        }


class PersistentTaskQueue:
    """Persistent task queue using SQLite for cross-process communication."""

    def __init__(self, db_path: str = "data/task_queue.db", max_size: int = 100):
        """Initialize the persistent task queue.

        Args:
            db_path: Path to SQLite database file.
            max_size: Maximum queue size.
        """
        self.db_path = Path(db_path)
        self.max_size = max_size
        self._lock = asyncio.Lock()
        self._initialize_db()

    def _initialize_db(self) -> None:
        """Initialize the SQLite database."""
        # Create the directory if it doesn't exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Create the database and tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create the tasks table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tasks (
            task_id TEXT PRIMARY KEY,
            task_type TEXT NOT NULL,
            params TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            result TEXT,
            error TEXT,
            created_at REAL NOT NULL,
            started_at REAL,
            completed_at REAL
        )
        """)

        # Create the queue table for task ordering
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS task_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT NOT NULL,
            created_at REAL NOT NULL,
            FOREIGN KEY (task_id) REFERENCES tasks (task_id)
        )
        """)

        conn.commit()
        conn.close()

        logger.info(f"Initialized persistent task queue database at {self.db_path}")

    async def enqueue(self, task_type: str, params: Dict[str, Any]) -> str:
        """Enqueue a new task.

        Args:
            task_type: Type of task (benchmark name).
            params: Task parameters.

        Returns:
            Task ID.
        """
        task_id = str(uuid.uuid4())
        created_at = time.time()

        async with self._lock:
            async with aiosqlite.connect(self.db_path) as db:
                # Insert task
                await db.execute(
                    "INSERT INTO tasks (task_id, task_type, params, status, created_at) VALUES (?, ?, ?, ?, ?)",
                    (task_id, task_type, json.dumps(params), TaskStatus.PENDING.value, created_at)
                )

                # Add to queue
                await db.execute(
                    "INSERT INTO task_queue (task_id, created_at) VALUES (?, ?)",
                    (task_id, created_at)
                )

                await db.commit()

        logger.info(f"Task {task_id} ({task_type}) enqueued to persistent queue")
        return task_id

    async def dequeue(self) -> Optional[str]:
        """Dequeue a task.

        Returns:
            Task ID or None if the queue is empty.
        """
        async with self._lock:
            async with aiosqlite.connect(self.db_path) as db:
                # Get the oldest pending task
                cursor = await db.execute("""
                    SELECT tq.task_id FROM task_queue tq
                    JOIN tasks t ON tq.task_id = t.task_id
                    WHERE t.status = ?
                    ORDER BY tq.created_at ASC
                    LIMIT 1
                """, (TaskStatus.PENDING.value,))

                row = await cursor.fetchone()
                if row:
                    task_id = row[0]
                    # Remove from queue
                    await db.execute("DELETE FROM task_queue WHERE task_id = ?", (task_id,))
                    await db.commit()
                    return task_id

        return None

    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID.

        Args:
            task_id: Task ID.

        Returns:
            Task or None if not found.
        """
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "SELECT task_id, task_type, params, status, result, error, created_at, started_at, completed_at FROM tasks WHERE task_id = ?",
                (task_id,)
            )
            row = await cursor.fetchone()

            if row:
                task = Task(row[0], row[1], json.loads(row[2]))
                task.status = TaskStatus(row[3])
                task.result = json.loads(row[4]) if row[4] else None
                task.error = row[5]
                task.created_at = row[6]
                task.started_at = row[7]
                task.completed_at = row[8]
                return task

        return None

    async def update_task_status(self, task_id: str, status: TaskStatus,
                                result: Any = None, error: str = None) -> bool:
        """Update task status.

        Args:
            task_id: Task ID.
            status: New task status.
            result: Task result (if completed).
            error: Error message (if failed).

        Returns:
            True if the task was updated, False otherwise.
        """
        async with self._lock:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if task exists
                cursor = await db.execute("SELECT task_id FROM tasks WHERE task_id = ?", (task_id,))
                if not await cursor.fetchone():
                    logger.warning(f"Task {task_id} not found")
                    return False

                # Prepare update data
                update_data = {"status": status.value}

                if status == TaskStatus.RUNNING:
                    update_data["started_at"] = time.time()
                elif status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                    update_data["completed_at"] = time.time()
                    if status == TaskStatus.COMPLETED and result is not None:
                        update_data["result"] = json.dumps(result)
                    elif status == TaskStatus.FAILED and error is not None:
                        update_data["error"] = error

                # Build update query
                set_clause = ", ".join([f"{key} = ?" for key in update_data.keys()])
                values = list(update_data.values()) + [task_id]

                await db.execute(f"UPDATE tasks SET {set_clause} WHERE task_id = ?", values)
                await db.commit()

        logger.info(f"Task {task_id} status updated to {status}")
        return True

    async def get_all_tasks(self) -> List[Task]:
        """Get all tasks.

        Returns:
            List of all tasks.
        """
        tasks = []
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "SELECT task_id, task_type, params, status, result, error, created_at, started_at, completed_at FROM tasks ORDER BY created_at DESC"
            )
            rows = await cursor.fetchall()

            for row in rows:
                task = Task(row[0], row[1], json.loads(row[2]))
                task.status = TaskStatus(row[3])
                task.result = json.loads(row[4]) if row[4] else None
                task.error = row[5]
                task.created_at = row[6]
                task.started_at = row[7]
                task.completed_at = row[8]
                tasks.append(task)

        return tasks

    async def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
        """List all tasks, optionally filtered by status.

        Args:
            status: Filter tasks by status.

        Returns:
            List of tasks as dictionaries.
        """
        tasks = []
        async with aiosqlite.connect(self.db_path) as db:
            if status:
                cursor = await db.execute(
                    "SELECT task_id, task_type, params, status, result, error, created_at, started_at, completed_at FROM tasks WHERE status = ? ORDER BY created_at DESC",
                    (status.value,)
                )
            else:
                cursor = await db.execute(
                    "SELECT task_id, task_type, params, status, result, error, created_at, started_at, completed_at FROM tasks ORDER BY created_at DESC"
                )
            rows = await cursor.fetchall()

            for row in rows:
                params = json.loads(row[2])
                task_dict = {
                    "task_id": row[0],
                    "benchmark": row[1],
                    "model": params.get("model"),
                    "status": row[3],
                    "params": params,
                    "created_at": row[6],
                    "started_at": row[7],
                    "completed_at": row[8]
                }
                tasks.append(task_dict)

        return tasks

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task.

        Args:
            task_id: Task ID.

        Returns:
            True if the task was cancelled, False otherwise.
        """
        async with self._lock:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if task exists and get its current status
                cursor = await db.execute("SELECT status FROM tasks WHERE task_id = ?", (task_id,))
                row = await cursor.fetchone()

                if not row:
                    logger.warning(f"Task {task_id} not found")
                    return False

                current_status = row[0]

                # Only allow cancellation of pending or running tasks
                if current_status not in [TaskStatus.PENDING.value, TaskStatus.RUNNING.value]:
                    logger.warning(f"Cannot cancel task {task_id} with status {current_status}")
                    return False

                # Update task status to cancelled
                await db.execute(
                    "UPDATE tasks SET status = ?, completed_at = ? WHERE task_id = ?",
                    (TaskStatus.CANCELLED.value, time.time(), task_id)
                )

                # Remove from queue if it's still pending
                if current_status == TaskStatus.PENDING.value:
                    await db.execute("DELETE FROM task_queue WHERE task_id = ?", (task_id,))

                await db.commit()

        logger.info(f"Task {task_id} cancelled")
        return True


# Global task queue instance
_task_queue_instance = None

def get_task_queue(max_size: int = 100) -> PersistentTaskQueue:
    """Get the global persistent task queue instance.

    Args:
        max_size: Maximum queue size.

    Returns:
        The global persistent task queue instance.
    """
    global _task_queue_instance
    if _task_queue_instance is None:
        _task_queue_instance = PersistentTaskQueue(max_size=max_size)
    return _task_queue_instance
