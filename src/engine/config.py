"""Configuration management for the Agent Evaluation Tool."""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv

from src.engine.logging_config import setup_logging

logger = logging.getLogger(__name__)


class BaseConfig:
    """Base configuration class with common functionality."""

    def __init__(self, config_path: str):
        """Initialize the configuration.

        Args:
            config_path: Path to the configuration file.
        """
        # Load environment variables from .env file first
        self._load_env_file()

        # Load configuration from file
        self.config_path = Path(config_path)
        self.config = self._load_config()

        # Configure logging based on config
        self._configure_logging()

    def _load_env_file(self) -> None:
        """Load environment variables from .env file if it exists."""
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(dotenv_path=env_file, override=False)
            logger.debug(f"Loaded environment variables from {env_file}")

    def _load_config(self) -> Dict[str, Any]:
        """Load the configuration file.

        Returns:
            The loaded configuration as a dictionary.
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")

        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f) or {}
                logger.info(f"Loaded configuration from {self.config_path}")
                return config
        except Exception as e:
            logger.error(f"Error loading config from {self.config_path}: {e}")
            raise

    def _configure_logging(self) -> None:
        """Configure logging based on the configuration."""
        logging_config = self.config.get("logging", {})
        setup_logging(logging_config)

    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value.

        Args:
            key: Dot-separated path to the configuration value.
            default: Default value to return if the key is not found.

        Returns:
            The configuration value or the default value if not found.
        """
        parts = key.split(".")
        value = self.config

        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return default

        return value

    def get_all(self) -> Dict[str, Any]:
        """Get the entire configuration.

        Returns:
            The entire configuration as a dictionary.
        """
        return self.config


class ServerConfig(BaseConfig):
    """Configuration manager for the API Server."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the server configuration.

        Args:
            config_path: Path to the server configuration file.
                        Defaults to config/server.yaml.
        """
        if config_path is None:
            config_path = "config/server.yaml"

        super().__init__(config_path)

        # Validate required server configuration sections
        self._validate_server_config()

    def _validate_server_config(self) -> None:
        """Validate that required server configuration sections exist."""
        required_sections = ["api", "queue", "storage", "logging"]
        missing_sections = []

        for section in required_sections:
            if section not in self.config:
                missing_sections.append(section)

        if missing_sections:
            logger.warning(f"Missing configuration sections in server config: {missing_sections}")

    @property
    def api_host(self) -> str:
        """Get API host."""
        return self.get("api.host", "0.0.0.0")

    @property
    def api_port(self) -> int:
        """Get API port."""
        return self.get("api.port", 8000)

    @property
    def api_debug(self) -> bool:
        """Get API debug mode."""
        return self.get("api.debug", False)

    @property
    def queue_max_size(self) -> int:
        """Get queue max size."""
        return self.get("queue.max_size", 100)


class WorkerConfig(BaseConfig):
    """Configuration manager for the Evaluation Worker."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the worker configuration.

        Args:
            config_path: Path to the worker configuration file.
                        Defaults to config/worker.yaml.
        """
        if config_path is None:
            config_path = "config/worker.yaml"

        super().__init__(config_path)

        # Validate required worker configuration sections
        self._validate_worker_config()

    def _validate_worker_config(self) -> None:
        """Validate that required worker configuration sections exist."""
        required_sections = ["worker", "llm", "benchmarks", "logging"]
        missing_sections = []

        for section in required_sections:
            if section not in self.config:
                missing_sections.append(section)

        if missing_sections:
            logger.warning(f"Missing configuration sections in worker config: {missing_sections}")

    @property
    def max_concurrent_tasks(self) -> int:
        """Get max concurrent tasks."""
        return self.get("worker.max_concurrent_tasks", 5)

    @property
    def timeout_seconds(self) -> int:
        """Get task timeout in seconds."""
        return self.get("worker.timeout_seconds", 3600)

    @property
    def model_concurrency(self) -> Dict[str, int]:
        """Get model concurrency limits."""
        return self.get("worker.model_concurrency", {})

    @property
    def llm_endpoints(self) -> list:
        """Get LLM endpoints configuration."""
        return self.get("llm.endpoints", [])

    @property
    def benchmarks_config(self) -> Dict[str, Any]:
        """Get benchmarks configuration."""
        return self.get("benchmarks", {})

    @property
    def frameworks_config(self) -> Dict[str, Any]:
        """Get frameworks configuration."""
        return self.get("frameworks", {})


def get_config(config_path: Optional[str] = None) -> WorkerConfig:
    """Get a configuration instance.

    Args:
        config_path: Path to the configuration file. If None, uses default worker config.

    Returns:
        Configuration instance.
    """
    if config_path is None:
        return WorkerConfig()
    else:
        return WorkerConfig(config_path)