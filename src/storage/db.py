"""Database interface for the Agent Evaluation Tool."""

import os
import json
import sqlite3
import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from datetime import datetime
import aiosqlite



logger = logging.getLogger(__name__)

class StorageBase:
    """Base class for storage implementations."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the storage.

        Args:
            config: Storage configuration.
        """
        self.config = config

    async def save_result(self, result: Dict[str, Any]) -> str:
        """Save an evaluation result.

        Args:
            result: Evaluation result.

        Returns:
            Result ID.
        """
        raise NotImplementedError

    async def get_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            Evaluation result or None if not found.
        """
        raise NotImplementedError

    async def list_results(self, benchmark: Optional[str] = None,
                          model: Optional[str] = None) -> List[Dict[str, Any]]:
        """List evaluation results.

        Args:
            benchmark: Filter by benchmark.
            model: Filter by model.

        Returns:
            List of evaluation results.
        """
        raise NotImplementedError

    async def delete_result(self, result_id: str) -> bool:
        """Delete an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            True if the result was deleted, False otherwise.
        """
        raise NotImplementedError


class SQLiteStorage(StorageBase):
    """SQLite storage implementation."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the SQLite storage.

        Args:
            config: Storage configuration.
        """
        super().__init__(config)
        # Use the storage.path from config, fallback to sqlite specific path, then default
        storage_path = config.get("storage", {}).get("path", "./data/results.db")
        sqlite_path = config.get("sqlite", {}).get("db_path", storage_path)
        self.db_path = Path(sqlite_path)
        self._initialize_db()

    def _initialize_db(self) -> None:
        """Initialize the SQLite database."""
        # Create the directory if it doesn't exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Create the database and tables
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create the results table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS results (
            id TEXT PRIMARY KEY,
            benchmark TEXT NOT NULL,
            model TEXT NOT NULL,
            task TEXT NOT NULL,
            framework TEXT,
            created_at TEXT NOT NULL,
            data TEXT NOT NULL
        )
        """)

        conn.commit()
        conn.close()

        logger.info(f"Initialized SQLite database at {self.db_path}")

    async def save_result(self, result: Dict[str, Any]) -> str:
        """Save an evaluation result.

        Args:
            result: Evaluation result.

        Returns:
            Result ID.
        """
        # Generate a result ID if not provided
        result_id = result.get("id", datetime.now().strftime("%Y%m%d%H%M%S"))

        # Extract metadata
        benchmark = result.get("benchmark", "unknown")
        model = result.get("model", "unknown")
        task = result.get("task", "unknown")
        framework = result.get("framework")
        created_at = datetime.now().isoformat()

        # Serialize the result
        data = json.dumps(result)

        # Save to the database
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                "INSERT OR REPLACE INTO results (id, benchmark, model, task, framework, created_at, data) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (result_id, benchmark, model, task, framework, created_at, data)
            )
            await db.commit()

        logger.info(f"Saved result {result_id} to SQLite database")
        return result_id

    async def get_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            Evaluation result or None if not found.
        """
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT data FROM results WHERE id = ?",
                (result_id,)
            ) as cursor:
                row = await cursor.fetchone()

                if row:
                    return json.loads(row[0])

                return None

    async def list_results(self, benchmark: Optional[str] = None,
                          model: Optional[str] = None) -> List[Dict[str, Any]]:
        """List evaluation results.

        Args:
            benchmark: Filter by benchmark.
            model: Filter by model.

        Returns:
            List of evaluation results.
        """
        query = "SELECT id, benchmark, model, task, framework, created_at FROM results"
        params = []

        if benchmark or model:
            query += " WHERE"

            if benchmark:
                query += " benchmark = ?"
                params.append(benchmark)

                if model:
                    query += " AND model = ?"
                    params.append(model)
            elif model:
                query += " model = ?"
                params.append(model)

        query += " ORDER BY created_at DESC"

        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(query, params) as cursor:
                results = []
                async for row in cursor:
                    results.append({
                        "id": row[0],
                        "benchmark": row[1],
                        "model": row[2],
                        "task": row[3],
                        "framework": row[4],
                        "created_at": row[5]
                    })

                return results

    async def delete_result(self, result_id: str) -> bool:
        """Delete an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            True if the result was deleted, False otherwise.
        """
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "DELETE FROM results WHERE id = ?",
                (result_id,)
            )
            await db.commit()

            return cursor.rowcount > 0


class JSONStorage(StorageBase):
    """JSON file storage implementation."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the JSON storage.

        Args:
            config: Storage configuration.
        """
        super().__init__(config)
        self.output_dir = Path(config.get("json", {}).get("output_dir", "./data/results"))
        self._initialize_storage()

    def _initialize_storage(self) -> None:
        """Initialize the JSON storage."""
        # Create the directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Initialized JSON storage at {self.output_dir}")

    async def save_result(self, result: Dict[str, Any]) -> str:
        """Save an evaluation result.

        Args:
            result: Evaluation result.

        Returns:
            Result ID.
        """
        # Generate a result ID if not provided
        result_id = result.get("id", datetime.now().strftime("%Y%m%d%H%M%S"))

        # Add metadata if not present
        if "created_at" not in result:
            result["created_at"] = datetime.now().isoformat()

        # Save to a JSON file
        file_path = self.output_dir / f"{result_id}.json"

        with open(file_path, "w") as f:
            json.dump(result, f, indent=2)

        logger.info(f"Saved result {result_id} to JSON file")
        return result_id

    async def get_result(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            Evaluation result or None if not found.
        """
        file_path = self.output_dir / f"{result_id}.json"

        if not file_path.exists():
            return None

        with open(file_path, "r") as f:
            return json.load(f)

    async def list_results(self, benchmark: Optional[str] = None,
                          model: Optional[str] = None) -> List[Dict[str, Any]]:
        """List evaluation results.

        Args:
            benchmark: Filter by benchmark.
            model: Filter by model.

        Returns:
            List of evaluation results.
        """
        results = []

        for file_path in self.output_dir.glob("*.json"):
            try:
                with open(file_path, "r") as f:
                    result = json.load(f)

                # Apply filters
                if benchmark and result.get("benchmark") != benchmark:
                    continue

                if model and result.get("model") != model:
                    continue

                # Add a summary
                results.append({
                    "id": result.get("id", file_path.stem),
                    "benchmark": result.get("benchmark", "unknown"),
                    "model": result.get("model", "unknown"),
                    "task": result.get("task", "unknown"),
                    "framework": result.get("framework"),
                    "created_at": result.get("created_at")
                })

            except Exception as e:
                logger.warning(f"Error loading result file {file_path}: {e}")

        # Sort by created_at
        results.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        return results

    async def delete_result(self, result_id: str) -> bool:
        """Delete an evaluation result.

        Args:
            result_id: Result ID.

        Returns:
            True if the result was deleted, False otherwise.
        """
        file_path = self.output_dir / f"{result_id}.json"

        if not file_path.exists():
            return False

        file_path.unlink()
        return True


# Global storage instance
_storage_instance = None

def get_storage(config=None) -> StorageBase:
    """Get the storage implementation based on configuration.

    Args:
        config: Configuration object (ServerConfig, WorkerConfig, or dict).
                If None, uses a default SQLite storage.

    Returns:
        Storage implementation.
    """
    global _storage_instance

    if _storage_instance is not None:
        return _storage_instance

    if config is None:
        # Default fallback configuration
        logger.warning("No configuration provided to get_storage(), using default SQLite storage")
        _storage_instance = SQLiteStorage({"storage": {"type": "sqlite", "path": "./data/results.db"}})
        return _storage_instance

    # Get storage configuration
    if hasattr(config, 'get'):
        # Config object with get method
        storage_type = config.get("storage.type", "sqlite")
        config_dict = config.get_all() if hasattr(config, 'get_all') else {"storage": {"type": storage_type}}
    else:
        # Dictionary config
        storage_type = config.get("storage", {}).get("type", "sqlite")
        config_dict = config

    if storage_type == "sqlite":
        _storage_instance = SQLiteStorage(config_dict)
    elif storage_type == "json":
        _storage_instance = JSONStorage(config_dict)
    else:
        logger.warning(f"Unsupported storage type: {storage_type}, falling back to SQLite")
        _storage_instance = SQLiteStorage(config_dict)

    return _storage_instance
